package plus.qdt.modules.order.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.order.fallback.YzhMessageFallback;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;

/**
 * 云中鹤消息服务 Feign 客户端
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@FeignClient(name = ServiceConstant.ORDER_SERVICE, contextId = "yzhMessage", fallback = YzhMessageFallback.class)
public interface YzhMessageClient {

    /**
     * 查询云中鹤消息列表
     *
     * @param requestParam 查询参数
     * @return 消息列表响应
     */
    @PostMapping("/feign/order/yzhMessage/queryMessageList")
    ResultMessage<YzhQueryMsgListResponse> queryMessageList(@RequestBody YzhQueryMsgListRequestParam requestParam);
}
