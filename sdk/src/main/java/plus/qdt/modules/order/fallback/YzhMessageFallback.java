package plus.qdt.modules.order.fallback;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.client.YzhMessageClient;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;

/**
 * 云中鹤消息服务 Feign 客户端降级处理
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
public class YzhMessageFallback implements YzhMessageClient {

    @Override
    public ResultMessage<YzhQueryMsgListResponse> queryMessageList(YzhQueryMsgListRequestParam requestParam) {
        log.error("调用云中鹤消息服务失败 - 查询消息列表，消息类型: {}", requestParam.getMessageType());
        
        // 返回空的响应结果，避免定时任务因为服务不可用而中断
        YzhQueryMsgListResponse emptyResponse = new YzhQueryMsgListResponse();
        emptyResponse.setSuccess(true);
        emptyResponse.setCode("00000");
        emptyResponse.setDesc("服务降级，返回空结果");
        
        YzhQueryMsgListResponse.YzhQueryMsgListResult emptyResult = new YzhQueryMsgListResponse.YzhQueryMsgListResult();
        emptyResult.setTotalCount(0);
        emptyResult.setList(java.util.Collections.emptyList());
        emptyResponse.setResult(emptyResult);
        
        return ResultUtil.data(emptyResponse);
    }
}
