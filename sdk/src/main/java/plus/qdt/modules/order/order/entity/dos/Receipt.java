package plus.qdt.modules.order.order.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 发票
 *
 * <AUTHOR>
 * @since 2020/11/28 11:38
 */
@Data
@TableName("li_receipt")
@Schema(title = "发票")
@EqualsAndHashCode(callSuper = true)
public class Receipt extends BaseStandardEntity {

    private static final long serialVersionUID = -8210927482915675995L;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "发票抬头")
    private String receiptTitle;

    @Schema(title = "纳税人识别号")
    private String taxpayerId;

    @Schema(title = "发票内容")
    private String receiptContent;

    @Schema(title = "发票金额")
    private Double receiptPrice;

    @Schema(title = "会员ID")
    private String memberId;

    @Schema(title = "会员名称")
    private String memberName;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "商家名称")
    private String storeName;

    @Schema(title = "发票状态 0未开 1已开")
    private Integer receiptStatus;

    @Schema(title = "发票详情")
    private String receiptDetail;

    @Schema(title = "发票附件地址")
    private String filePath;

    /**
        @see plus.qdt.modules.order.enums.ReceiptTypeEnum
     */
    @Schema(title="发票类型")
    private String receiptType;
}
