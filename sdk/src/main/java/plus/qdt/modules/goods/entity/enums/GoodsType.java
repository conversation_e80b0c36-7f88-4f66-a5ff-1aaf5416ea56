package plus.qdt.modules.goods.entity.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.StringUtils;

import java.util.List;

/**
 * 商品分类枚举
 *
 * <AUTHOR>
 * @since 2.0
 */
@Getter
@AllArgsConstructor
public enum GoodsType {

    GOODS("", "商品", ""),
    VIP_CARDS("1882348297787600898", "会员年卡", ""),
    ENTREPRENEURIAL_GIFT_PACK("1882324104090021889", "创业大礼包", ""),
    STREET_GIFT_PACK("1887026961573801986", "镇代大礼包", "street"),
    AREA_GIFT_PACK("1887027055773675521", "县代大礼包", "district"),
    VILLAGE_GIFT_PACK("1887025018713468929", "村代大礼包", "village"),
    STORE_YEAR_FEE("1926879642637783041", "商家年费", ""),

    ;

    private final String categoryId;
    private final String description;
    private final String regionLevel;

    /**
     * 根据商品类别判断是否为数商代理大礼包
     * @param categoryId 商品分类
     * @return {@link Boolean}
     * <AUTHOR>
     */
    public static boolean isDigitalAgency(String categoryId) {
        if (StringUtils.isNotBlank(categoryId)) {
            return STREET_GIFT_PACK.getCategoryId().equals(categoryId) ||
                    AREA_GIFT_PACK.getCategoryId().equals(categoryId) ||
                    VILLAGE_GIFT_PACK.getCategoryId().equals(categoryId);
        }
        return false;
    }

    /**
     * 根据商品类别判断是否为数商代理大礼包
     * @param categoryId 商品分类
     * @return {@link Boolean}
     * <AUTHOR>
     */
    public static GoodsType digitalAgency(String categoryId) {
        GoodsType goodsType = EnumUtil.getBy(GoodsType::getCategoryId, categoryId);
        switch (goodsType) {
            case VILLAGE_GIFT_PACK, STREET_GIFT_PACK, AREA_GIFT_PACK -> {
                return goodsType;
            }
            default -> throw new ServiceException("数商礼包错误");
        }
    }

    /**
     * 是否为企道通商品类型
     * @param categoryId 商品类型
     * @return {@link Boolean}
     * <AUTHOR>
     */
    public static boolean isQdt(String categoryId) {
        for (GoodsType value : values()) {
            if (value.categoryId.equals(categoryId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取企道通商品类型
     * @return {@link List<String>}
     * <AUTHOR>
     */
    public static List<String> getQdtCategoryIds() {
        return List.of(VIP_CARDS.getCategoryId(), ENTREPRENEURIAL_GIFT_PACK.getCategoryId(),
                STREET_GIFT_PACK.getCategoryId(), AREA_GIFT_PACK.getCategoryId(),
                VILLAGE_GIFT_PACK.getCategoryId(), STORE_YEAR_FEE.getCategoryId());
    }

}
