package plus.qdt.modules.system.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.system.entity.MessageRecord;
import plus.qdt.modules.system.fallback.MessageRecordFallback;

/**
 * 消息记录 Feign 客户端
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "messageRecord", fallback = MessageRecordFallback.class)
public interface MessageRecordClient {

    /**
     * 根据业务类型和业务键查询消息记录
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 消息记录
     */
    @GetMapping("/feign/system/messageRecord/biz/{bizType}/{bizKey}")
    ResultMessage<MessageRecord> getByBizTypeAndKey(@PathVariable String bizType, @PathVariable String bizKey);

    /**
     * 检查同样的消息是否已经被消费
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 是否已被消费
     */
    @GetMapping("/feign/system/messageRecord/consumed/{bizType}/{bizKey}")
    ResultMessage<Boolean> isMessageConsumed(@PathVariable String bizType, @PathVariable String bizKey);

    /**
     * 创建消息记录
     *
     * @param messageRecord 消息记录
     * @return 创建结果
     */
    @PostMapping("/feign/system/messageRecord")
    ResultMessage<MessageRecord> create(@RequestBody MessageRecord messageRecord);
}
