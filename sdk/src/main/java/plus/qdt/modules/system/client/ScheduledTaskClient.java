package plus.qdt.modules.system.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.fallback.ScheduledTaskFallback;

import java.util.List;

/**
 * 定时任务服务 Feign 客户端
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "scheduledTask", fallback = ScheduledTaskFallback.class)
public interface ScheduledTaskClient {

    /**
     * 获取所有启用的定时任务
     *
     * @return 启用的定时任务列表
     */
    @GetMapping("/feign/system/scheduledTask/enabled")
    ResultMessage<List<ScheduledTaskConfig>> getEnabledTasks();

    /**
     * 根据任务编码获取任务配置
     *
     * @param taskCode 任务编码
     * @return 任务配置
     */
    @GetMapping("/feign/system/scheduledTask/getByTaskCode/{taskCode}")
    ResultMessage<ScheduledTaskConfig> getByTaskCode(@PathVariable String taskCode);

    /**
     * 启用任务
     *
     * @param taskCode 任务编码
     * @param updater 更新人
     * @return 是否成功
     */
    @PostMapping("/feign/system/scheduledTask/enable/{taskCode}")
    ResultMessage<Boolean> enableTask(@PathVariable String taskCode, @RequestParam String updater);

    /**
     * 禁用任务
     *
     * @param taskCode 任务编码
     * @param updater 更新人
     * @return 是否成功
     */
    @PostMapping("/feign/system/scheduledTask/disable/{taskCode}")
    ResultMessage<Boolean> disableTask(@PathVariable String taskCode, @RequestParam String updater);

    /**
     * 更新任务参数
     *
     * @param taskCode 任务编码
     * @param taskParams 任务参数
     * @param updater 更新人
     * @return 是否成功
     */
    @PostMapping("/feign/system/scheduledTask/updateParams/{taskCode}")
    ResultMessage<Boolean> updateTaskParams(@PathVariable String taskCode, 
                                           @RequestParam String taskParams, 
                                           @RequestParam String updater);

    /**
     * 更新任务频率
     *
     * @param taskCode 任务编码
     * @param fixedRate 固定频率（毫秒）
     * @param updater 更新人
     * @return 是否成功
     */
    @PostMapping("/feign/system/scheduledTask/updateRate/{taskCode}")
    ResultMessage<Boolean> updateTaskRate(@PathVariable String taskCode, 
                                         @RequestParam Long fixedRate, 
                                         @RequestParam String updater);

    /**
     * 更新任务Cron表达式
     *
     * @param taskCode 任务编码
     * @param cronExpression Cron表达式
     * @param updater 更新人
     * @return 是否成功
     */
    @PostMapping("/feign/system/scheduledTask/updateCron/{taskCode}")
    ResultMessage<Boolean> updateTaskCron(@PathVariable String taskCode, 
                                         @RequestParam String cronExpression, 
                                         @RequestParam String updater);

    /**
     * 记录任务开始执行
     *
     * @param taskCode 任务编码
     * @return 操作结果
     */
    @PostMapping("/feign/system/scheduledTask/recordStart/{taskCode}")
    ResultMessage<Void> recordTaskStart(@PathVariable String taskCode);

    /**
     * 记录任务执行成功
     *
     * @param taskCode 任务编码
     * @param message 执行消息
     * @return 操作结果
     */
    @PostMapping("/feign/system/scheduledTask/recordSuccess/{taskCode}")
    ResultMessage<Void> recordTaskSuccess(@PathVariable String taskCode, @RequestParam String message);

    /**
     * 记录任务执行失败
     *
     * @param taskCode 任务编码
     * @param errorMessage 错误消息
     * @return 操作结果
     */
    @PostMapping("/feign/system/scheduledTask/recordFailure/{taskCode}")
    ResultMessage<Void> recordTaskFailure(@PathVariable String taskCode, @RequestParam String errorMessage);
}
