package plus.qdt.modules.system.fallback;

import lombok.extern.slf4j.Slf4j;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.ResultMessage;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.modules.system.client.MessageRecordClient;
import plus.qdt.modules.system.entity.MessageRecord;

/**
 * 消息记录 Feign 客户端降级处理
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
public class MessageRecordFallback implements MessageRecordClient {

    @Override
    public ResultMessage<MessageRecord> getByBizTypeAndKey(String bizType, String bizKey) {
        log.error("调用消息记录服务失败，业务类型: {}, 业务键: {}", bizType, bizKey);
        return ResultUtil.error("消息记录服务不可用");
    }

    @Override
    public ResultMessage<Boolean> isMessageConsumed(String bizType, String bizKey) {
        log.error("检查消息消费状态失败，业务类型: {}, 业务键: {}", bizType, bizKey);
        // 降级时返回false，允许继续处理，避免因为服务不可用导致消息丢失
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<MessageRecord> create(MessageRecord messageRecord) {
        log.error("创建消息记录失败，消息ID: {}", messageRecord.getMessageId());
        return ResultUtil.error("消息记录服务不可用");
    }
}
