package plus.qdt.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@TableName("message_record")
@Schema(title = "通用消息表")
@Accessors(chain = true)
@NoArgsConstructor
public class MessageRecord {
    @Schema(title = "主键ID", description = "消息记录的唯一标识", example = "123456789")
    private Long id;

    @Schema(title = "消息唯一ID", description = "由业务类型+业务键+时间戳生成的唯一标识",
            example = "INVOICE_CREATE_ORDER_20230702153000_12345")
    private String messageId;

    /**
     * @see plus.qdt.modules.system.entity.enums.MessageBizTypeEnum
     */

    @Schema(title = "业务类型", description = "区分不同业务场景的标识",
            example = "INVOICE_CREATE", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizType;

    @Schema(title = "业务唯一键", description = "关联原始业务数据的唯一标识（如订单号、发票ID等）",
            example = "ORD20230702123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizKey;

    @Schema(title = "消息内容", description = "JSON格式的业务数据",
            example = "{\"amount\": 199.99, \"orderNo\": \"ORD20230702123456\"}",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String messageContent;

    /**
     * @see plus.qdt.modules.system.entity.enums.MessageStatusEnum
     */

    @Schema(title = "消息状态", description = "0-待发送 1-已发送 2-发送失败",
            example = "0", allowableValues = {"0", "1", "2"})
    private Integer messageStatus;

    @Schema(title = "目标队列", description = "消息要发送到的MQ队列名称",
            example = "invoice_create_queue", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetQueue;

    @Schema(title = "当前重试次数", description = "消息已尝试发送的次数", example = "3")
    private Integer retryCount;

    @Schema(title = "最大重试次数", description = "允许的最大重试次数", example = "10")
    private Integer maxRetry;

    @Schema(title = "下次重试时间", description = "计划下次重试的时间戳", example = "2023-07-02 15:35:00")
    private Date nextRetryTime;

    @Schema(title = "最后错误信息", description = "最近一次发送失败的错误详情",
            example = "MQ连接超时: Connection timed out")
    private String lastError;

    @Schema(title = "创建时间", description = "消息记录的创建时间", example = "2023-07-02 15:30:00")
    private Date createdAt;

    @Schema(title = "更新时间", description = "消息记录的最后更新时间", example = "2023-07-02 15:32:00")
    private Date updatedAt;
}
