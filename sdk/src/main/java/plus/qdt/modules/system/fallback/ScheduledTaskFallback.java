package plus.qdt.modules.system.fallback;

import lombok.extern.slf4j.Slf4j;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.system.client.ScheduledTaskClient;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 定时任务服务 Feign 客户端降级处理
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
public class ScheduledTaskFallback implements ScheduledTaskClient {

    @Override
    public ResultMessage<List<ScheduledTaskConfig>> getEnabledTasks() {
        log.error("调用定时任务服务失败 - 获取启用的任务列表");
        return ResultUtil.data(new ArrayList<>());
    }

    @Override
    public ResultMessage<ScheduledTaskConfig> getByTaskCode(String taskCode) {
        log.error("调用定时任务服务失败 - 根据任务编码获取任务配置，任务编码: {}", taskCode);
        return ResultUtil.data(null);
    }

    @Override
    public ResultMessage<Boolean> enableTask(String taskCode, String updater) {
        log.error("调用定时任务服务失败 - 启用任务，任务编码: {}, 操作人: {}", taskCode, updater);
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<Boolean> disableTask(String taskCode, String updater) {
        log.error("调用定时任务服务失败 - 禁用任务，任务编码: {}, 操作人: {}", taskCode, updater);
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<Boolean> updateTaskParams(String taskCode, String taskParams, String updater) {
        log.error("调用定时任务服务失败 - 更新任务参数，任务编码: {}, 操作人: {}", taskCode, updater);
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<Boolean> updateTaskRate(String taskCode, Long fixedRate, String updater) {
        log.error("调用定时任务服务失败 - 更新任务频率，任务编码: {}, 频率: {}ms, 操作人: {}", taskCode, fixedRate, updater);
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<Boolean> updateTaskCron(String taskCode, String cronExpression, String updater) {
        log.error("调用定时任务服务失败 - 更新任务Cron表达式，任务编码: {}, Cron: {}, 操作人: {}", taskCode, cronExpression, updater);
        return ResultUtil.data(false);
    }

    @Override
    public ResultMessage<Void> recordTaskStart(String taskCode) {
        log.error("调用定时任务服务失败 - 记录任务开始执行，任务编码: {}", taskCode);
        return ResultUtil.success();
    }

    @Override
    public ResultMessage<Void> recordTaskSuccess(String taskCode, String message) {
        log.error("调用定时任务服务失败 - 记录任务执行成功，任务编码: {}, 消息: {}", taskCode, message);
        return ResultUtil.success();
    }

    @Override
    public ResultMessage<Void> recordTaskFailure(String taskCode, String errorMessage) {
        log.error("调用定时任务服务失败 - 记录任务执行失败，任务编码: {}, 错误: {}", taskCode, errorMessage);
        return ResultUtil.success();
    }
}
