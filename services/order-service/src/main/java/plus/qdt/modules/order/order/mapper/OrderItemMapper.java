package plus.qdt.modules.order.order.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import plus.qdt.modules.member.entity.vo.GoodsCollectionVO;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.vo.OrderSimpleVO;

import java.util.List;

/**
 * 子订单数据处理层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:34 下午
 */
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * 获取等待操作订单子项目
     *
     * @param queryWrapper 查询条件
     * @return 订单子项列表
     */
    @Select("SELECT * FROM li_order_item AS oi INNER JOIN li_order AS o ON oi.order_sn=o.sn ${ew.customSqlSegment}")
    List<OrderItem> waitOperationOrderItem(@Param(Constants.WRAPPER) Wrapper<OrderSimpleVO> queryWrapper);

    /**
     * 未申请售后的订单，进行售后过期标识
     *
     * @param expiredTime 过期时间
     */
    @Update("update li_order_item  oi INNER JOIN li_order AS o ON oi.order_sn=o.sn  set after_sale_status = 'EXPIRED' where after_sale_status = " +
            "'NOT_APPLIED' and o.complete_time < #{expiredTime}")
    void expiredAfterSaleStatus(DateTime expiredTime);

    /**
     * 检查售后表，根据售后表中的业务判定是否需要更新售后状态
     *
     * @param expiredTime 过期时间
     */
    @Update("""
            UPDATE li_order_item AS oi
            INNER JOIN li_order AS o ON oi.order_sn = o.sn
            INNER JOIN li_after_sale AS af ON af.order_item_sn = oi.sn
            SET oi.after_sale_status = 'EXPIRED'
            WHERE
            oi.after_sale_status in ('ALREADY_APPLIED','PART_AFTER_SALE')
            AND af.service_status in ('COMPLETE','REFUSE','SELLER_TERMINATION','BUYER_CANCEL')
            AND o.complete_time <  #{expiredTime}
    """)
    void expiredAfterSaleStatusExecuteByAfterSale(DateTime expiredTime);

    /**
     * 获取店铺销量最高的三个商品(非年卡、礼包类、下架)
     * @param id 店铺ID
     * @return {@link List} {@link GoodsCollectionVO}
     * <AUTHOR>
     */
    List<GoodsCollectionVO> getTop3GoodsIdByStoreId(@Param("storeId") String id, @Param("categoryIds") List<String> categoryIds,
                                                    @Param("goodsIds") List<String> goodsIds);

    /**
     * 根据用户ID+商品类型列表组合查询出数量（购买大礼包的用户数量）
     * @param userIds 用户ID集合
     * @param categoryIds 商品类别ID集合
     * @return {@link Long}
     * <AUTHOR>
     */
    Long getVipOrPromotionCount(@Param("userIds") List<String> userIds, @Param("categoryIds") List<String> categoryIds);
}