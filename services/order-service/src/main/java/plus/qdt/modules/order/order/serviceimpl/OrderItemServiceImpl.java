
package plus.qdt.modules.order.order.serviceimpl;

import cn.hutool.core.date.DateTime;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.CollectionUtils;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.goods.entity.enums.GoodsType;
import plus.qdt.modules.member.entity.vo.StoreCollectionVO;
import plus.qdt.modules.order.aftersale.entity.enums.ComplaintStatusEnum;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dto.OrderSearchParams;
import plus.qdt.modules.order.order.entity.enums.CommentStatusEnum;
import plus.qdt.modules.order.order.entity.enums.OrderComplaintStatusEnum;
import plus.qdt.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import plus.qdt.modules.order.order.mapper.OrderItemMapper;
import plus.qdt.modules.order.order.service.OrderItemService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 子订单业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
@AllArgsConstructor
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemService {

    private final GoodsClient goodsClient;

    @Override
    @Transactional
    public void updateCommentStatus(String orderItemSn, CommentStatusEnum commentStatusEnum) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(OrderItem::getCommentStatus, commentStatusEnum.name());
        lambdaUpdateWrapper.eq(OrderItem::getSn, orderItemSn);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional
    public void updateAfterSaleStatus(String orderItemSn, OrderItemAfterSaleStatusEnum orderItemAfterSaleStatusEnum) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(OrderItem::getAfterSaleStatus, orderItemAfterSaleStatusEnum.name());
        lambdaUpdateWrapper.eq(OrderItem::getSn, orderItemSn);
        this.update(lambdaUpdateWrapper);
    }

    /**
     * 更新订单可投诉状态
     *
     * @param orderSn            订单sn
     * @param skuId              商品skuId
     * @param complainId         订单交易投诉ID
     * @param complainStatusEnum 修改状态
     */
    @Override
    @Transactional
    public void updateOrderItemsComplainStatus(String orderSn, String skuId, String complainId, ComplaintStatusEnum complainStatusEnum) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItem::getOrderSn, orderSn).eq(OrderItem::getSkuId, skuId);
        OrderItem orderItem = getOne(queryWrapper);
        if (orderItem == null) {
            throw new ServiceException(ResultCode.ORDER_ITEM_NOT_EXIST);
        }
        orderItem.setComplainId(complainId);
        orderItem.setComplainStatus(complainStatusEnum.name());
        updateById(orderItem);
    }

    @Override
    public OrderItem getBySn(String sn) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(OrderItem::getSn, sn);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<OrderItem> getByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(OrderItem::getOrderSn, orderSn);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public OrderItem getByOrderSnAndSkuId(String orderSn, String skuId) {
        return this.getOne(new LambdaQueryWrapper<OrderItem>()
                .eq(OrderItem::getOrderSn, orderSn)
                .eq(OrderItem::getSkuId, skuId));
    }

    /**
     * 获取等待操作订单子项目
     *
     * @param searchParams 查询条件
     * @return 订单子项列表
     */
    @Override
    public List<OrderItem> waitOperationOrderItem(OrderSearchParams searchParams) {
        return this.baseMapper.waitOperationOrderItem(searchParams.queryWrapper());
    }


    /**
     * 修改订单投诉状态
     *
     * @param orderItemIdList 获取订单货物ID
     * @return 是否操作成功
     */
    @Override
    @Transactional
    public boolean updateComplainStatus(List<String> orderItemIdList) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = new LambdaUpdateWrapper<OrderItem>()
                .set(OrderItem::getComplainStatus, OrderComplaintStatusEnum.EXPIRED.name())
                .in(OrderItem::getId, orderItemIdList);
        return this.update(lambdaUpdateWrapper);
    }

    @Override
    public void expiredAfterSaleStatus(DateTime expiredTime) {
        baseMapper.expiredAfterSaleStatus(expiredTime);
        baseMapper.expiredAfterSaleStatusExecuteByAfterSale(expiredTime);
    }

    @Override
    public List<StoreCollectionVO.Goods> getTop3ByStoreId(String id) {
        List<String> ids = goodsClient.getGoodsIds(GoodsMarketEnum.DOWN, id);
        return CollectionUtils.copyList(baseMapper.getTop3GoodsIdByStoreId(id, GoodsType.getQdtCategoryIds(), ids), StoreCollectionVO.Goods.class);
    }

    @Override
    public Long getVipOrPromotionCount(List<String> userIds, List<String> categoryIds) {
        return baseMapper.getVipOrPromotionCount(userIds, categoryIds);
    }
}