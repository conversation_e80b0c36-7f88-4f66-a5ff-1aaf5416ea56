package plus.qdt.controller.order.scheduledTask;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.modules.system.client.ScheduledTaskClient;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.entity.dto.TaskConfigRequestParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 定时任务管理Controller
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@RestController
@RequestMapping("/order/scheduled-task")
@RequiredArgsConstructor
@Tag(name = "定时任务管理", description = "定时任务配置和管理接口")
public class ScheduledTaskController {

    private final ScheduledTaskClient scheduledTaskClient;

    /**
     * 查询所有定时任务
     *
     * @return 定时任务列表
     */
    @Operation(summary = "查询所有定时任务")
    @GetMapping("/list")
    public ResultMessage<List<ScheduledTaskConfig>> listTasks() {
        ResultMessage<List<ScheduledTaskConfig>> result = scheduledTaskClient.getEnabledTasks();
        return result.isSuccess() ? result : ResultUtil.error("获取定时任务列表失败");
    }

    /**
     * 查询启用的定时任务
     *
     * @return 启用的定时任务列表
     */
    @Operation(summary = "查询启用的定时任务")
    @GetMapping("/enabled")
    public ResultMessage<List<ScheduledTaskConfig>> getEnabledTasks() {
        List<ScheduledTaskConfig> tasks = scheduledTaskService.getEnabledTasks();
        return ResultUtil.data(tasks);
    }

    /**
     * 根据任务编码查询任务配置
     *
     * @param taskCode 任务编码
     * @return 任务配置
     */
    @Operation(summary = "根据任务编码查询任务配置")
    @GetMapping("/{taskCode}")
    public ResultMessage<ScheduledTaskConfig> getTaskByCode(@PathVariable String taskCode) {
        ScheduledTaskConfig task = scheduledTaskService.getByTaskCode(taskCode);
        return ResultUtil.data(task);
    }

    /**
     * 启用任务
     *
     * @param taskCode 任务编码
     * @return 操作结果
     */
    @Operation(summary = "启用任务")
    @PostMapping("/{taskCode}/enable")
    public ResultMessage<Boolean> enableTask(@PathVariable String taskCode) {
        boolean result = scheduledTaskService.enableTask(taskCode, "admin");
        return ResultUtil.data(result);
    }

    /**
     * 禁用任务
     *
     * @param taskCode 任务编码
     * @return 操作结果
     */
    @Operation(summary = "禁用任务")
    @PostMapping("/{taskCode}/disable")
    public ResultMessage<Boolean> disableTask(@PathVariable String taskCode) {
        boolean result = scheduledTaskService.disableTask(taskCode, "admin");
        return ResultUtil.data(result);
    }

    /**
     * 更新任务参数
     *
     * @param taskCode 任务编码
     * @param taskParams 任务参数（JSON格式）
     * @return 操作结果
     */
    @Operation(summary = "更新任务参数")
    @PostMapping("/{taskCode}/params")
    public ResultMessage<Boolean> updateTaskParams(
            @PathVariable String taskCode,
            @RequestBody @NotBlank(message = "任务参数不能为空") String taskParams) {
        boolean result = scheduledTaskService.updateTaskParams(taskCode, taskParams, "admin");
        return ResultUtil.data(result);
    }

    /**
     * 更新任务频率
     *
     * @param taskCode 任务编码
     * @param fixedRate 固定频率（毫秒）
     * @return 操作结果
     */
    @Operation(summary = "更新任务频率")
    @PostMapping("/{taskCode}/rate")
    public ResultMessage<Boolean> updateTaskRate(
            @PathVariable String taskCode,
            @RequestParam @NotNull(message = "任务频率不能为空") Long fixedRate) {
        boolean result = scheduledTaskService.updateTaskRate(taskCode, fixedRate, "admin");
        return ResultUtil.data(result);
    }

    /**
     * 更新任务Cron表达式
     *
     * @param taskCode 任务编码
     * @param cronExpression Cron表达式
     * @return 操作结果
     */
    @Operation(summary = "更新任务Cron表达式")
    @PostMapping("/{taskCode}/cron")
    public ResultMessage<Boolean> updateTaskCron(
            @PathVariable String taskCode,
            @RequestParam @NotBlank(message = "Cron表达式不能为空") String cronExpression) {
        boolean result = scheduledTaskService.updateTaskCron(taskCode, cronExpression, "admin");
        return ResultUtil.data(result);
    }

    /**
     * 云中鹤商品消息任务快速配置
     *
     * @param enabled 是否启用
     * @param pageSize 每页条数
     * @param intervalMinutes 执行间隔（分钟）
     * @return 操作结果
     */
    @Operation(summary = "云中鹤商品消息任务快速配置")
    @PostMapping("/yzh-goods-message/config")
    public ResultMessage<Boolean> configYzhGoodsMessageTask(
            @RequestParam Boolean enabled,
            @RequestParam(defaultValue = "25") Integer pageSize,
            @RequestParam(defaultValue = "10") Integer intervalMinutes) {
        
        String taskCode = "YZH_GOODS_MESSAGE_QUERY";
        
        try {
            // 1. 更新任务参数
            String taskParams = String.format("{\"messageType\":1,\"pageSize\":%d,\"pageNum\":1}", pageSize);
            ResultMessage<Boolean> updateParamsResult = scheduledTaskClient.updateTaskParams(taskCode, taskParams, "admin");
            if (!updateParamsResult.isSuccess()) {
                return ResultUtil.error("更新任务参数失败");
            }

            // 2. 更新任务频率
            Long fixedRate = intervalMinutes * 60 * 1000L; // 转换为毫秒
            ResultMessage<Boolean> updateRateResult = scheduledTaskClient.updateTaskRate(taskCode, fixedRate, "admin");
            if (!updateRateResult.isSuccess()) {
                return ResultUtil.error("更新任务频率失败");
            }

            // 3. 启用或禁用任务
            ResultMessage<Boolean> taskResult;
            if (enabled) {
                taskResult = scheduledTaskClient.enableTask(taskCode, "admin");
            } else {
                taskResult = scheduledTaskClient.disableTask(taskCode, "admin");
            }

            if (!taskResult.isSuccess()) {
                return ResultUtil.error(enabled ? "启用任务失败" : "禁用任务失败");
            }

            boolean result = taskResult.getResult();
            
            return ResultUtil.data(result);
            
        } catch (Exception e) {
            log.error("配置云中鹤商品消息任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("配置失败: " + e.getMessage());
        }
    }

    /**
     * 通用任务配置接口
     *
     * @param taskCode 任务编码
     * @param configParam 配置参数
     * @return 操作结果
     */
    @Operation(summary = "通用任务配置接口")
    @PostMapping("/{taskCode}/config")
    public ResultMessage<Boolean> configTask(
            @PathVariable String taskCode,
            @RequestBody @Valid TaskConfigRequestParam configParam) {

        try {
            // 1. 更新任务参数
            if (configParam.getMessageType() != null || configParam.getPageSize() != null) {
                String taskParams = configParam.toTaskParamsJson();
                ResultMessage<Boolean> updateParamsResult = scheduledTaskClient.updateTaskParams(taskCode, taskParams, "admin");
                if (!updateParamsResult.isSuccess()) {
                    return ResultUtil.error("更新任务参数失败");
                }
            }

            // 2. 更新任务频率或Cron表达式
            if (configParam.getCronExpression() != null && !configParam.getCronExpression().trim().isEmpty()) {
                ResultMessage<Boolean> updateCronResult = scheduledTaskClient.updateTaskCron(taskCode, configParam.getCronExpression(), "admin");
                if (!updateCronResult.isSuccess()) {
                    return ResultUtil.error("更新任务Cron表达式失败");
                }
            } else if (configParam.getIntervalMinutes() != null) {
                Long fixedRate = configParam.toFixedRate();
                ResultMessage<Boolean> updateRateResult = scheduledTaskClient.updateTaskRate(taskCode, fixedRate, "admin");
                if (!updateRateResult.isSuccess()) {
                    return ResultUtil.error("更新任务频率失败");
                }
            }

            // 3. 启用或禁用任务
            ResultMessage<Boolean> taskResult;
            if (configParam.getEnabled()) {
                taskResult = scheduledTaskClient.enableTask(taskCode, "admin");
            } else {
                taskResult = scheduledTaskClient.disableTask(taskCode, "admin");
            }

            if (!taskResult.isSuccess()) {
                return ResultUtil.error(configParam.getEnabled() ? "启用任务失败" : "禁用任务失败");
            }

            boolean result = taskResult.getResult();
            return ResultUtil.data(result);

        } catch (Exception e) {
            log.error("配置任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error("配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量配置云中鹤消息任务
     *
     * @param configParam 配置参数
     * @return 操作结果
     */
    @Operation(summary = "批量配置云中鹤消息任务")
    @PostMapping("/yzh-message/batch-config")
    public ResultMessage<String> batchConfigYzhMessageTasks(@RequestBody @Valid TaskConfigRequestParam configParam) {

        try {
            String[] taskCodes = {
                "YZH_GOODS_MESSAGE_QUERY",
                "YZH_SALES_ORDER_MESSAGE_QUERY",
                "YZH_AFTER_SALE_MESSAGE_QUERY"
            };

            int successCount = 0;
            StringBuilder resultMessage = new StringBuilder();

            for (String taskCode : taskCodes) {
                try {
                    // 根据任务类型设置对应的messageType
                    TaskConfigRequestParam taskConfig = TaskConfigRequestParam.builder()
                            .enabled(configParam.getEnabled())
                            .messageType(getMessageTypeByTaskCode(taskCode))
                            .pageSize(configParam.getPageSize())
                            .intervalMinutes(configParam.getIntervalMinutes())
                            .cronExpression(configParam.getCronExpression())
                            .build();

                    // 配置任务
                    ResultMessage<Boolean> result = configTask(taskCode, taskConfig);
                    if (result.isSuccess() && Boolean.TRUE.equals(result.getResult())) {
                        successCount++;
                        resultMessage.append(String.format("任务 %s 配置成功; ", taskCode));
                    } else {
                        resultMessage.append(String.format("任务 %s 配置失败; ", taskCode));
                    }

                } catch (Exception e) {
                    resultMessage.append(String.format("任务 %s 配置异常: %s; ", taskCode, e.getMessage()));
                }
            }

            String finalMessage = String.format("批量配置完成，成功 %d/%d 个任务。详情: %s",
                    successCount, taskCodes.length, resultMessage.toString());

            return ResultUtil.data(finalMessage);

        } catch (Exception e) {
            log.error("批量配置云中鹤消息任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("批量配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务编码获取对应的消息类型
     *
     * @param taskCode 任务编码
     * @return 消息类型
     */
    private Integer getMessageTypeByTaskCode(String taskCode) {
        switch (taskCode) {
            case "YZH_GOODS_MESSAGE_QUERY":
                return 1; // 商品消息
            case "YZH_SALES_ORDER_MESSAGE_QUERY":
                return 2; // 销售订单消息
            case "YZH_AFTER_SALE_MESSAGE_QUERY":
                return 3; // 售后订单消息
            default:
                return 1; // 默认商品消息
        }
    }
}
