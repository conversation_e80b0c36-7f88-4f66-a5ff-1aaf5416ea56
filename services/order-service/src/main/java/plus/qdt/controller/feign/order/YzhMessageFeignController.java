package plus.qdt.controller.feign.order;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.client.YzhMessageClient;
import plus.qdt.modules.order.tpi.service.YzhMessageService;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;

/**
 * 云中鹤消息服务 Feign 实现
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequestMapping("/feign/order/yzhMessage")
@RequiredArgsConstructor
public class YzhMessageFeignController implements YzhMessageClient {

    private final YzhMessageService yzhMessageService;

    @Override
    public ResultMessage<YzhQueryMsgListResponse> queryMessageList(YzhQueryMsgListRequestParam requestParam) {
        try {
            log.info("Feign调用 - 查询云中鹤消息列表，消息类型: {}, 页码: {}, 每页条数: {}",
                    requestParam.getMessageType(), requestParam.getPageNum(), requestParam.getPageSize());
            
            YzhQueryMsgListResponse response = yzhMessageService.queryMessageList(requestParam);
            
            log.info("Feign调用 - 云中鹤消息列表查询完成，消息类型: {}, 总条数: {}",
                    requestParam.getMessageType(),
                    response.getResult() != null ? response.getResult().getTotalCount() : 0);
            
            return ResultUtil.data(response);
        } catch (Exception e) {
            log.error("Feign调用 - 查询云中鹤消息列表失败，消息类型: {}, 错误: {}", 
                     requestParam.getMessageType(), e.getMessage(), e);
            return ResultUtil.error("查询云中鹤消息列表失败: " + e.getMessage());
        }
    }
}
