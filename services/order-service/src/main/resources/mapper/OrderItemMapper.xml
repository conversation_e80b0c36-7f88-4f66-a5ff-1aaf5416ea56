<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="plus.qdt.modules.order.order.mapper.OrderItemMapper">

    <select id="getTop3GoodsIdByStoreId" resultType="plus.qdt.modules.member.entity.vo.GoodsCollectionVO">
        SELECT DISTINCT
            sku_id,
            b.goods_id,
            image,
            b.order_num
        FROM
            li_order_item AS a
                INNER JOIN (
                SELECT
                    goods_id,
                    SUM(num) AS order_num
                FROM
                    li_order_item
                GROUP BY
                    goods_id) AS b ON
                a.goods_id = b.goods_id
        WHERE
            a.order_sn IN(SELECT sn FROM li_order WHERE pay_status = 'PAID' AND store_id = #{storeId})
            AND a.category_id NOT IN <foreach collection="categoryIds" item="item" open="(" separator="," close=")">#{item}</foreach>
            AND a.goods_id NOT IN <foreach collection="goodsIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        ORDER BY b.order_num DESC
            LIMIT 3
    </select>

    <select id="getVipOrPromotionCount" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            li_order_item
        WHERE
        order_sn IN (SELECT sn FROM li_order WHERE pay_status = 'PAID')
        <if test="categoryIds != null and categoryIds.size() > 0">
            AND category_id IN <foreach collection="categoryIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN <foreach collection="userIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
    </select>
</mapper>