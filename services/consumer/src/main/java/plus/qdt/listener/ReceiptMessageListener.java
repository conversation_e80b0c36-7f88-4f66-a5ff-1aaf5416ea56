package plus.qdt.listener;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import plus.qdt.common.utils.ResultMessage;
import plus.qdt.modules.order.order.client.ReceiptClient;
import plus.qdt.modules.order.order.entity.dos.Receipt;
import plus.qdt.modules.system.client.MessageRecordClient;
import plus.qdt.routing.ReceiptRoutingKey;

/***
 * 发票创建通知
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReceiptMessageListener {

    private final ReceiptClient receiptClientl;
    private final MessageRecordClient messageRecordClient;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ("${qdt.amqp.receipt}" + "_" + ReceiptRoutingKey.RECEIPT_CREATE)),
            exchange = @Exchange(value = "${qdt.amqp.receipt}"),
            key = ReceiptRoutingKey.RECEIPT_CREATE))
    public void onMessage(String receiptMessageJson) {
        try {
            log.info("接收到发票创建消息: {}", receiptMessageJson);

            Receipt receipt = JSONUtil.toBean(receiptMessageJson, Receipt.class);

            // 构建业务键，使用订单编号作为唯一标识
            String bizType = "RECEIPT_CREATE";
            String bizKey = receipt.getOrderSn();

            // 检查该消息是否已经被消费过
            ResultMessage<Boolean> consumedResult = messageRecordClient.isMessageConsumed(bizType, bizKey);

            if (consumedResult.isSuccess() && Boolean.TRUE.equals(consumedResult.getResult())) {
                log.info("发票消息已被消费过，跳过处理，订单号: {}", bizKey);
                return;
            }

            // 如果消息没有被消费过，则执行保存发票逻辑
            log.info("开始处理发票创建，订单号: {}", bizKey);
            Receipt savedReceipt = receiptClientl.saveReceipt(receipt);

            if (savedReceipt != null) {
                log.info("发票创建成功，订单号: {}, 发票ID: {}", bizKey, savedReceipt.getId());
            } else {
                log.warn("发票创建失败，订单号: {}", bizKey);
            }

        } catch (Exception e) {
            log.error("处理发票创建消息失败: {}, 错误: {}", receiptMessageJson, e.getMessage(), e);
            // 这里可以考虑将失败的消息发送到死信队列或者重试队列
        }
    }
}
