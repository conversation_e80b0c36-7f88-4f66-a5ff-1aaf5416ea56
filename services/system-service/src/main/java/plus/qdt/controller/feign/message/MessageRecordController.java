package plus.qdt.controller.feign.message;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.system.entity.MessageRecord;
import plus.qdt.modules.system.entity.vo.MessageRecordVO;
import plus.qdt.modules.system.service.MessageRecordService;

import java.util.Date;
import java.util.List;

/**
 * 消息记录控制器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequestMapping("/feign/system/messageRecord")
@RequiredArgsConstructor
@Tag(name = "消息记录管理", description = "消息记录的增删改查操作")
public class MessageRecordController {

    private final MessageRecordService messageRecordService;

    @Operation(summary = "分页查询消息记录")
    @GetMapping("/page")
    public ResultMessage<Page<MessageRecord>> getPage(MessageRecordVO messageRecordVO, PageVO pageVO) {
        try {
            Page<MessageRecord> page = messageRecordService.getPage(messageRecordVO, pageVO);
            return ResultUtil.data(page);
        } catch (Exception e) {
            log.error("分页查询消息记录失败: {}", e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "根据ID查询消息记录")
    @GetMapping("/{id}")
    public ResultMessage<MessageRecord> getById(
            @Parameter(description = "消息记录ID") @PathVariable Long id) {
        try {
            MessageRecord messageRecord = messageRecordService.getById(id);
            if (messageRecord == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "消息记录不存在");
            }
            return ResultUtil.data(messageRecord);
        } catch (Exception e) {
            log.error("根据ID查询消息记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "根据消息ID查询消息记录")
    @GetMapping("/messageId/{messageId}")
    public ResultMessage<MessageRecord> getByMessageId(
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        try {
            MessageRecord messageRecord = messageRecordService.getByMessageId(messageId);
            if (messageRecord == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "消息记录不存在");
            }
            return ResultUtil.data(messageRecord);
        } catch (Exception e) {
            log.error("根据消息ID查询消息记录失败，消息ID: {}, 错误: {}", messageId, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "根据业务类型和业务键查询消息记录")
    @GetMapping("/biz/{bizType}/{bizKey}")
    public ResultMessage<MessageRecord> getByBizTypeAndKey(
            @Parameter(description = "业务类型") @PathVariable String bizType,
            @Parameter(description = "业务键") @PathVariable String bizKey) {
        try {
            MessageRecord messageRecord = messageRecordService.getByBizTypeAndKey(bizType, bizKey);
            if (messageRecord == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "消息记录不存在");
            }
            return ResultUtil.data(messageRecord);
        } catch (Exception e) {
            log.error("根据业务类型和业务键查询消息记录失败，业务类型: {}, 业务键: {}, 错误: {}",
                    bizType, bizKey, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "创建消息记录")
    @PostMapping
    public ResultMessage<MessageRecord> create(@Valid @RequestBody MessageRecord messageRecord) {
        try {
            // 设置创建时间和更新时间
            Date now = new Date();
            messageRecord.setCreatedAt(now);
            messageRecord.setUpdatedAt(now);

            // 设置默认值
            if (messageRecord.getMessageStatus() == null) {
                messageRecord.setMessageStatus(0); // 默认待发送
            }
            if (messageRecord.getRetryCount() == null) {
                messageRecord.setRetryCount(0); // 默认重试次数为0
            }
            if (messageRecord.getMaxRetry() == null) {
                messageRecord.setMaxRetry(3); // 默认最大重试次数为3
            }

            boolean saved = messageRecordService.save(messageRecord);
            if (saved) {
                return ResultUtil.data(messageRecord);
            } else {
                return ResultUtil.error(ResultCode.ERROR.code(), "创建消息记录失败");
            }
        } catch (Exception e) {
            log.error("创建消息记录失败: {}", e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "更新消息记录")
    @PutMapping("/{id}")
    public ResultMessage<MessageRecord> update(
            @Parameter(description = "消息记录ID") @PathVariable Long id,
            @Valid @RequestBody MessageRecord messageRecord) {
        try {
            // 检查记录是否存在
            MessageRecord existingRecord = messageRecordService.getById(id);
            if (existingRecord == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "消息记录不存在");
            }

            // 设置ID和更新时间
            messageRecord.setId(id);
            messageRecord.setUpdatedAt(new Date());

            boolean updated = messageRecordService.updateById(messageRecord);
            if (updated) {
                return ResultUtil.data(messageRecord);
            } else {
                return ResultUtil.error(ResultCode.ERROR.code(), "更新消息记录失败");
            }
        } catch (Exception e) {
            log.error("更新消息记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "删除消息记录")
    @DeleteMapping("/{id}")
    public ResultMessage<Boolean> delete(
            @Parameter(description = "消息记录ID") @PathVariable Long id) {
        try {
            // 检查记录是否存在
            MessageRecord existingRecord = messageRecordService.getById(id);
            if (existingRecord == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "消息记录不存在");
            }

            boolean deleted = messageRecordService.removeById(id);
            return ResultUtil.data(deleted);
        } catch (Exception e) {
            log.error("删除消息记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "批量删除消息记录")
    @DeleteMapping("/batch")
    public ResultMessage<Boolean> batchDelete(@RequestBody List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return ResultUtil.error(ResultCode.PARAMS_ERROR.code(), "删除ID列表不能为空");
            }

            boolean deleted = messageRecordService.removeByIds(ids);
            return ResultUtil.data(deleted);
        } catch (Exception e) {
            log.error("批量删除消息记录失败，IDs: {}, 错误: {}", ids, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "更新消息状态")
    @PutMapping("/{id}/status")
    public ResultMessage<Boolean> updateStatus(
            @Parameter(description = "消息记录ID") @PathVariable Long id,
            @Parameter(description = "消息状态") @RequestParam Integer status,
            @Parameter(description = "错误信息") @RequestParam(required = false) String errorMsg) {
        try {
            Boolean result = messageRecordService.updateMessageStatus(id, status, errorMsg);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新消息状态失败，ID: {}, 状态: {}, 错误: {}", id, status, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "增加重试次数")
    @PutMapping("/{id}/retry")
    public ResultMessage<Boolean> incrementRetryCount(
            @Parameter(description = "消息记录ID") @PathVariable Long id) {
        try {
            Boolean result = messageRecordService.incrementRetryCount(id);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("增加重试次数失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "检查同样的消息是否已经被消费")
    @GetMapping("/consumed/{bizType}/{bizKey}")
    public ResultMessage<Boolean> isMessageConsumed(
            @Parameter(description = "业务类型") @PathVariable String bizType,
            @Parameter(description = "业务键") @PathVariable String bizKey) {
        try {
            Boolean result = messageRecordService.isMessageConsumed(bizType, bizKey);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("检查消息消费状态失败，业务类型: {}, 业务键: {}, 错误: {}",
                    bizType, bizKey, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }
}
