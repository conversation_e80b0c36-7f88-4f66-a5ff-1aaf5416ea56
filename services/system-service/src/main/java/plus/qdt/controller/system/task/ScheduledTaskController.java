package plus.qdt.controller.system.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.service.ScheduledTaskService;

import java.util.List;

/**
 * 定时任务管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequestMapping("/system/scheduledTask")
@RequiredArgsConstructor
@Tag(name = "定时任务管理", description = "定时任务的配置和管理")
public class ScheduledTaskController {

    private final ScheduledTaskService scheduledTaskService;

    @Operation(summary = "分页查询定时任务")
    @GetMapping("/page")
    public ResultMessage<Page<ScheduledTaskConfig>> getPage(PageVO pageVO) {
        try {
            Page<ScheduledTaskConfig> page = scheduledTaskService.page(
                    new Page<>(pageVO.getPageNumber(), pageVO.getPageSize()));
            return ResultUtil.data(page);
        } catch (Exception e) {
            log.error("分页查询定时任务失败: {}", e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "获取所有启用的定时任务")
    @GetMapping("/enabled")
    public ResultMessage<List<ScheduledTaskConfig>> getEnabledTasks() {
        try {
            List<ScheduledTaskConfig> tasks = scheduledTaskService.getEnabledTasks();
            return ResultUtil.data(tasks);
        } catch (Exception e) {
            log.error("获取启用的定时任务失败: {}", e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "根据任务编码获取任务配置")
    @GetMapping("/{taskCode}")
    public ResultMessage<ScheduledTaskConfig> getByTaskCode(
            @Parameter(description = "任务编码") @PathVariable String taskCode) {
        try {
            ScheduledTaskConfig taskConfig = scheduledTaskService.getByTaskCode(taskCode);
            if (taskConfig == null) {
                return ResultUtil.error(ResultCode.USER_NOT_EXIST.code(), "任务配置不存在");
            }
            return ResultUtil.data(taskConfig);
        } catch (Exception e) {
            log.error("根据任务编码获取任务配置失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "启用任务")
    @PutMapping("/{taskCode}/enable")
    public ResultMessage<Boolean> enableTask(
            @Parameter(description = "任务编码") @PathVariable String taskCode,
            @Parameter(description = "操作人") @RequestParam(defaultValue = "admin") String updater) {
        try {
            boolean result = scheduledTaskService.enableTask(taskCode, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("启用任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "禁用任务")
    @PutMapping("/{taskCode}/disable")
    public ResultMessage<Boolean> disableTask(
            @Parameter(description = "任务编码") @PathVariable String taskCode,
            @Parameter(description = "操作人") @RequestParam(defaultValue = "admin") String updater) {
        try {
            boolean result = scheduledTaskService.disableTask(taskCode, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("禁用任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "更新任务参数")
    @PutMapping("/{taskCode}/params")
    public ResultMessage<Boolean> updateTaskParams(
            @Parameter(description = "任务编码") @PathVariable String taskCode,
            @Parameter(description = "任务参数") @RequestParam String taskParams,
            @Parameter(description = "操作人") @RequestParam(defaultValue = "admin") String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskParams(taskCode, taskParams, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务参数失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "更新任务频率")
    @PutMapping("/{taskCode}/rate")
    public ResultMessage<Boolean> updateTaskRate(
            @Parameter(description = "任务编码") @PathVariable String taskCode,
            @Parameter(description = "固定频率（毫秒）") @RequestParam Long fixedRate,
            @Parameter(description = "操作人") @RequestParam(defaultValue = "admin") String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskRate(taskCode, fixedRate, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务频率失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "更新任务Cron表达式")
    @PutMapping("/{taskCode}/cron")
    public ResultMessage<Boolean> updateTaskCron(
            @Parameter(description = "任务编码") @PathVariable String taskCode,
            @Parameter(description = "Cron表达式") @RequestParam String cronExpression,
            @Parameter(description = "操作人") @RequestParam(defaultValue = "admin") String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskCron(taskCode, cronExpression, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务Cron表达式失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "发票消息补偿任务快速配置")
    @PostMapping("/receipt-compensation/config")
    public ResultMessage<Boolean> configReceiptCompensationTask(
            @Parameter(description = "是否启用") @RequestParam Boolean enabled,
            @Parameter(description = "执行间隔（小时）") @RequestParam(defaultValue = "1") Integer intervalHours,
            @Parameter(description = "最大重试时间（小时）") @RequestParam(defaultValue = "24") Integer maxRetryHours) {
        
        String taskCode = "RECEIPT_MESSAGE_COMPENSATION";
        
        try {
            // 1. 更新任务参数
            String taskParams = String.format("{\"bizType\":\"RECEIPT_CREATE\",\"maxRetryHours\":%d,\"batchSize\":100}", 
                    maxRetryHours);
            scheduledTaskService.updateTaskParams(taskCode, taskParams, "admin");
            
            // 2. 更新任务频率
            Long fixedRate = intervalHours * 60 * 60 * 1000L; // 转换为毫秒
            scheduledTaskService.updateTaskRate(taskCode, fixedRate, "admin");
            
            // 3. 更新Cron表达式
            String cronExpression = String.format("0 0 */%d * * ?", intervalHours);
            scheduledTaskService.updateTaskCron(taskCode, cronExpression, "admin");
            
            // 4. 启用或禁用任务
            boolean result;
            if (enabled) {
                result = scheduledTaskService.enableTask(taskCode, "admin");
            } else {
                result = scheduledTaskService.disableTask(taskCode, "admin");
            }
            
            return ResultUtil.data(result);
            
        } catch (Exception e) {
            log.error("配置发票消息补偿任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "手动触发发票消息补偿任务")
    @PostMapping("/receipt-compensation/trigger")
    public ResultMessage<String> triggerReceiptCompensationTask() {
        try {
            // 这里可以通过ApplicationContext获取ReceiptMessageCompensationTask bean并手动执行
            // 或者发送一个立即执行的消息到队列
            log.info("手动触发发票消息补偿任务");
            return ResultUtil.data("任务触发成功，请查看日志了解执行情况");
        } catch (Exception e) {
            log.error("手动触发发票消息补偿任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("触发失败: " + e.getMessage());
        }
    }

    @Operation(summary = "云中鹤定时任务快速配置")
    @PostMapping("/yzh-message/config")
    public ResultMessage<Boolean> configYzhMessageTask(
            @Parameter(description = "任务编码") @RequestParam String taskCode,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled,
            @Parameter(description = "消息类型") @RequestParam(defaultValue = "1") Integer messageType,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "100") Integer pageSize,
            @Parameter(description = "执行间隔（分钟）") @RequestParam(defaultValue = "10") Integer intervalMinutes) {

        try {
            // 验证任务编码
            if (!isValidYzhTaskCode(taskCode)) {
                return ResultUtil.error("无效的云中鹤任务编码");
            }

            // 1. 更新任务参数
            String taskParams = String.format("{\"messageType\":%d,\"pageSize\":%d,\"pageNum\":1}",
                    messageType, pageSize);
            boolean updateParamsResult = scheduledTaskService.updateTaskParams(taskCode, taskParams, "admin");
            if (!updateParamsResult) {
                return ResultUtil.error("更新任务参数失败");
            }

            // 2. 更新任务频率
            Long fixedRate = intervalMinutes * 60 * 1000L; // 转换为毫秒
            boolean updateRateResult = scheduledTaskService.updateTaskRate(taskCode, fixedRate, "admin");
            if (!updateRateResult) {
                return ResultUtil.error("更新任务频率失败");
            }

            // 3. 启用或禁用任务
            boolean result;
            if (enabled) {
                result = scheduledTaskService.enableTask(taskCode, "admin");
            } else {
                result = scheduledTaskService.disableTask(taskCode, "admin");
            }

            return ResultUtil.data(result);

        } catch (Exception e) {
            log.error("配置云中鹤定时任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error("配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量配置云中鹤定时任务")
    @PostMapping("/yzh-message/batch-config")
    public ResultMessage<String> batchConfigYzhMessageTasks(
            @Parameter(description = "是否启用") @RequestParam Boolean enabled,
            @Parameter(description = "消息类型") @RequestParam(defaultValue = "1") Integer messageType,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "100") Integer pageSize,
            @Parameter(description = "执行间隔（分钟）") @RequestParam(defaultValue = "10") Integer intervalMinutes) {

        try {
            String[] yzhTaskCodes = {"YZH_GOODS_MESSAGE_QUERY", "YZH_SALES_ORDER_MESSAGE_QUERY"};
            int successCount = 0;
            StringBuilder resultMessage = new StringBuilder();

            for (String taskCode : yzhTaskCodes) {
                try {
                    // 配置单个任务
                    ResultMessage<Boolean> result = configYzhMessageTask(taskCode, enabled, messageType, pageSize, intervalMinutes);
                    if (result.isSuccess() && Boolean.TRUE.equals(result.getResult())) {
                        successCount++;
                        resultMessage.append(String.format("任务 %s 配置成功; ", taskCode));
                    } else {
                        resultMessage.append(String.format("任务 %s 配置失败; ", taskCode));
                    }
                } catch (Exception e) {
                    log.error("配置任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
                    resultMessage.append(String.format("任务 %s 配置异常: %s; ", taskCode, e.getMessage()));
                }
            }

            String finalMessage = String.format("批量配置完成，成功: %d/%d。详情: %s",
                    successCount, yzhTaskCodes.length, resultMessage.toString());

            return ResultUtil.data(finalMessage);

        } catch (Exception e) {
            log.error("批量配置云中鹤定时任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("批量配置失败: " + e.getMessage());
        }
    }

    /**
     * 验证是否为有效的云中鹤任务编码
     */
    private boolean isValidYzhTaskCode(String taskCode) {
        return "YZH_GOODS_MESSAGE_QUERY".equals(taskCode) ||
               "YZH_SALES_ORDER_MESSAGE_QUERY".equals(taskCode);
    }
}
