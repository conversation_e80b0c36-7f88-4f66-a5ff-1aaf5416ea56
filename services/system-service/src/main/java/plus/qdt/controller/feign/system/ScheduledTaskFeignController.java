package plus.qdt.controller.feign.system;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.system.client.ScheduledTaskClient;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.service.ScheduledTaskService;

import java.util.List;

/**
 * 定时任务服务 Feign 实现
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ScheduledTaskFeignController implements ScheduledTaskClient {

    private final ScheduledTaskService scheduledTaskService;

    @Override
    public ResultMessage<List<ScheduledTaskConfig>> getEnabledTasks() {
        try {
            List<ScheduledTaskConfig> tasks = scheduledTaskService.getEnabledTasks();
            return ResultUtil.data(tasks);
        } catch (Exception e) {
            log.error("获取启用的定时任务失败: {}", e.getMessage(), e);
            return ResultUtil.error("获取启用的定时任务失败");
        }
    }

    @Override
    public ResultMessage<ScheduledTaskConfig> getByTaskCode(String taskCode) {
        try {
            ScheduledTaskConfig task = scheduledTaskService.getByTaskCode(taskCode);
            return ResultUtil.data(task);
        } catch (Exception e) {
            log.error("根据任务编码获取任务配置失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error("获取任务配置失败");
        }
    }

    @Override
    public ResultMessage<Boolean> enableTask(String taskCode, String updater) {
        try {
            boolean result = scheduledTaskService.enableTask(taskCode, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("启用任务失败，任务编码: {}, 操作人: {}, 错误: {}", taskCode, updater, e.getMessage(), e);
            return ResultUtil.error("启用任务失败");
        }
    }

    @Override
    public ResultMessage<Boolean> disableTask(String taskCode, String updater) {
        try {
            boolean result = scheduledTaskService.disableTask(taskCode, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("禁用任务失败，任务编码: {}, 操作人: {}, 错误: {}", taskCode, updater, e.getMessage(), e);
            return ResultUtil.error("禁用任务失败");
        }
    }

    @Override
    public ResultMessage<Boolean> updateTaskParams(String taskCode, String taskParams, String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskParams(taskCode, taskParams, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务参数失败，任务编码: {}, 操作人: {}, 错误: {}", taskCode, updater, e.getMessage(), e);
            return ResultUtil.error("更新任务参数失败");
        }
    }

    @Override
    public ResultMessage<Boolean> updateTaskRate(String taskCode, Long fixedRate, String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskRate(taskCode, fixedRate, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务频率失败，任务编码: {}, 频率: {}ms, 操作人: {}, 错误: {}", 
                     taskCode, fixedRate, updater, e.getMessage(), e);
            return ResultUtil.error("更新任务频率失败");
        }
    }

    @Override
    public ResultMessage<Boolean> updateTaskCron(String taskCode, String cronExpression, String updater) {
        try {
            boolean result = scheduledTaskService.updateTaskCron(taskCode, cronExpression, updater);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("更新任务Cron表达式失败，任务编码: {}, Cron: {}, 操作人: {}, 错误: {}", 
                     taskCode, cronExpression, updater, e.getMessage(), e);
            return ResultUtil.error("更新任务Cron表达式失败");
        }
    }

    @Override
    public ResultMessage<Void> recordTaskStart(String taskCode) {
        try {
            scheduledTaskService.recordTaskStart(taskCode);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("记录任务开始执行失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return ResultUtil.error("记录任务开始执行失败");
        }
    }

    @Override
    public ResultMessage<Void> recordTaskSuccess(String taskCode, String message) {
        try {
            scheduledTaskService.recordTaskSuccess(taskCode, message);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("记录任务执行成功失败，任务编码: {}, 消息: {}, 错误: {}", taskCode, message, e.getMessage(), e);
            return ResultUtil.error("记录任务执行成功失败");
        }
    }

    @Override
    public ResultMessage<Void> recordTaskFailure(String taskCode, String errorMessage) {
        try {
            scheduledTaskService.recordTaskFailure(taskCode, errorMessage);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("记录任务执行失败失败，任务编码: {}, 错误消息: {}, 异常: {}", taskCode, errorMessage, e.getMessage(), e);
            return ResultUtil.error("记录任务执行失败失败");
        }
    }
}
