package plus.qdt.modules.system.serviceimpl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.system.entity.MessageRecord;
import plus.qdt.modules.system.entity.enums.MessageStatusEnum;
import plus.qdt.modules.system.entity.vo.MessageRecordVO;
import plus.qdt.modules.system.mapper.MessageRecordMapper;
import plus.qdt.modules.system.service.MessageRecordService;
import plus.qdt.mybatis.util.PageUtil;

/**
 * 消息记录业务层实现
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageRecordServiceImpl extends ServiceImpl<MessageRecordMapper, MessageRecord> implements MessageRecordService {

    @Override
    public Page<MessageRecord> getPage(MessageRecordVO messageRecordVO, PageVO pageVO) {
        return this.page(PageUtil.initPage(pageVO), messageRecordVO.buildQueryWrapper());
    }

    @Override
    public MessageRecord getByMessageId(String messageId) {
        return this.baseMapper.selectByMessageId(messageId);
    }

    @Override
    public MessageRecord getByBizTypeAndKey(String bizType, String bizKey) {
        return this.baseMapper.selectByBizTypeAndKey(bizType, bizKey);
    }

    @Override
    public Boolean updateMessageStatus(Long id, Integer status, String errorMsg) {
        try {
            int result = this.baseMapper.updateMessageStatus(id, status, errorMsg);
            return result > 0;
        } catch (Exception e) {
            log.error("更新消息状态失败，ID: {}, 状态: {}, 错误: {}", id, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean incrementRetryCount(Long id) {
        try {
            int result = this.baseMapper.incrementRetryCount(id);
            return result > 0;
        } catch (Exception e) {
            log.error("增加重试次数失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean isMessageConsumed(String bizType, String bizKey) {
        try {
            MessageRecord messageRecord = this.getByBizTypeAndKey(bizType, bizKey);
            if (messageRecord == null) {
                // 没有找到消息记录，说明没有被消费过
                return false;
            }

            // 检查消息状态是否为已发送（已消费）
            return MessageStatusEnum.SENT.getCode()== messageRecord.getMessageStatus();
        } catch (Exception e) {
            log.error("检查消息消费状态失败，业务类型: {}, 业务键: {}, 错误: {}",
                    bizType, bizKey, e.getMessage(), e);
            // 异常情况下返回false，允许继续处理
            return false;
        }
    }


}
