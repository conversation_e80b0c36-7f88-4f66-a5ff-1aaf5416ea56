package plus.qdt.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;

/**
 * 定时任务配置数据访问层
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface ScheduledTaskConfigMapper extends BaseMapper<ScheduledTaskConfig> {

    /**
     * 根据任务编码查询任务配置
     *
     * @param taskCode 任务编码
     * @return 任务配置
     */
    @Select("SELECT * FROM scheduled_task_config WHERE task_code = #{taskCode} AND deleted = 0 LIMIT 1")
    ScheduledTaskConfig selectByTaskCode(@Param("taskCode") String taskCode);

    /**
     * 更新任务状态
     *
     * @param taskCode 任务编码
     * @param taskStatus 任务状态
     * @param updater 更新人
     * @return 影响行数
     */
    @Update("UPDATE scheduled_task_config SET task_status = #{taskStatus}, updater = #{updater}, update_time = NOW() WHERE task_code = #{taskCode}")
    int updateTaskStatus(@Param("taskCode") String taskCode, @Param("taskStatus") String taskStatus, @Param("updater") String updater);

    /**
     * 更新任务参数
     *
     * @param taskCode 任务编码
     * @param taskParams 任务参数
     * @param updater 更新人
     * @return 影响行数
     */
    @Update("UPDATE scheduled_task_config SET task_params = #{taskParams}, updater = #{updater}, update_time = NOW() WHERE task_code = #{taskCode}")
    int updateTaskParams(@Param("taskCode") String taskCode, @Param("taskParams") String taskParams, @Param("updater") String updater);

    /**
     * 更新任务频率
     *
     * @param taskCode 任务编码
     * @param fixedRate 固定频率
     * @param updater 更新人
     * @return 影响行数
     */
    @Update("UPDATE scheduled_task_config SET fixed_rate = #{fixedRate}, updater = #{updater}, update_time = NOW() WHERE task_code = #{taskCode}")
    int updateTaskRate(@Param("taskCode") String taskCode, @Param("fixedRate") Long fixedRate, @Param("updater") String updater);

    /**
     * 更新任务Cron表达式
     *
     * @param taskCode 任务编码
     * @param cronExpression Cron表达式
     * @param updater 更新人
     * @return 影响行数
     */
    @Update("UPDATE scheduled_task_config SET cron_expression = #{cronExpression}, updater = #{updater}, update_time = NOW() WHERE task_code = #{taskCode}")
    int updateTaskCron(@Param("taskCode") String taskCode, @Param("cronExpression") String cronExpression, @Param("updater") String updater);

    /**
     * 更新任务执行统计信息
     *
     * @param taskCode 任务编码
     * @param lastExecuteResult 执行结果
     * @param lastExecuteMessage 执行消息
     * @return 影响行数
     */
    @Update("UPDATE scheduled_task_config SET " +
            "last_execute_time = NOW(), " +
            "execute_count = execute_count + 1, " +
            "success_count = CASE WHEN #{lastExecuteResult} = 'SUCCESS' THEN success_count + 1 ELSE success_count END, " +
            "fail_count = CASE WHEN #{lastExecuteResult} = 'FAILURE' THEN fail_count + 1 ELSE fail_count END, " +
            "last_execute_result = #{lastExecuteResult}, " +
            "last_execute_message = #{lastExecuteMessage}, " +
            "update_time = NOW() " +
            "WHERE task_code = #{taskCode}")
    int updateTaskExecuteInfo(@Param("taskCode") String taskCode, 
                             @Param("lastExecuteResult") String lastExecuteResult, 
                             @Param("lastExecuteMessage") String lastExecuteMessage);
}
