package plus.qdt.modules.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.system.entity.MessageRecord;
import plus.qdt.modules.system.entity.vo.MessageRecordVO;

/**
 * 消息记录业务层接口
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface MessageRecordService extends IService<MessageRecord> {

    /**
     * 分页查询消息记录
     *
     * @param messageRecordVO 查询条件
     * @param pageVO 分页参数
     * @return 分页结果
     */
    Page<MessageRecord> getPage(MessageRecordVO messageRecordVO, PageVO pageVO);

    /**
     * 根据消息ID查询消息记录
     *
     * @param messageId 消息ID
     * @return 消息记录
     */
    MessageRecord getByMessageId(String messageId);

    /**
     * 根据业务类型和业务键查询消息记录
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 消息记录
     */
    MessageRecord getByBizTypeAndKey(String bizType, String bizKey);

    /**
     * 更新消息状态
     *
     * @param id 消息记录ID
     * @param status 新状态
     * @param errorMsg 错误信息（可选）
     * @return 是否更新成功
     */
    Boolean updateMessageStatus(Long id, Integer status, String errorMsg);

    /**
     * 增加重试次数
     *
     * @param id 消息记录ID
     * @return 是否更新成功
     */
    Boolean incrementRetryCount(Long id);

    /**
     * 检查同样的消息是否已经被消费
     * 根据业务类型和业务键查询是否存在已发送状态的消息记录
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 是否已被消费
     */
    Boolean isMessageConsumed(String bizType, String bizKey);

    /**
     * 检查同样的消息是否已经被消费
     * 根据业务类型和业务键查询是否存在已发送状态的消息记录
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 是否已被消费
     */
    Boolean isMessageConsumed(String bizType, String bizKey);
}
