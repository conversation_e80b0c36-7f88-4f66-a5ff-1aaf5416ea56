package plus.qdt.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;

import java.util.List;

/**
 * 定时任务服务接口
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface ScheduledTaskService extends IService<ScheduledTaskConfig> {

    /**
     * 获取所有启用的定时任务
     *
     * @return 启用的定时任务列表
     */
    List<ScheduledTaskConfig> getEnabledTasks();

    /**
     * 根据任务编码获取任务配置
     *
     * @param taskCode 任务编码
     * @return 任务配置
     */
    ScheduledTaskConfig getByTaskCode(String taskCode);

    /**
     * 启用任务
     *
     * @param taskCode 任务编码
     * @param updater 更新人
     * @return 是否成功
     */
    boolean enableTask(String taskCode, String updater);

    /**
     * 禁用任务
     *
     * @param taskCode 任务编码
     * @param updater 更新人
     * @return 是否成功
     */
    boolean disableTask(String taskCode, String updater);

    /**
     * 更新任务参数
     *
     * @param taskCode 任务编码
     * @param taskParams 任务参数（JSON格式）
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateTaskParams(String taskCode, String taskParams, String updater);

    /**
     * 更新任务频率
     *
     * @param taskCode 任务编码
     * @param fixedRate 固定频率（毫秒）
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateTaskRate(String taskCode, Long fixedRate, String updater);

    /**
     * 更新任务Cron表达式
     *
     * @param taskCode 任务编码
     * @param cronExpression Cron表达式
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateTaskCron(String taskCode, String cronExpression, String updater);

    /**
     * 记录任务执行开始
     *
     * @param taskCode 任务编码
     */
    void recordTaskStart(String taskCode);

    /**
     * 记录任务执行成功
     *
     * @param taskCode 任务编码
     * @param message 执行消息
     */
    void recordTaskSuccess(String taskCode, String message);

    /**
     * 记录任务执行失败
     *
     * @param taskCode 任务编码
     * @param message 失败消息
     */
    void recordTaskFailure(String taskCode, String message);

    /**
     * 计算下次执行时间
     *
     * @param taskConfig 任务配置
     * @return 下次执行时间
     */
    Long calculateNextExecuteTime(ScheduledTaskConfig taskConfig);
}
