package plus.qdt.modules.system.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.entity.enums.TaskStatusEnum;
import plus.qdt.modules.system.mapper.ScheduledTaskConfigMapper;
import plus.qdt.modules.system.service.ScheduledTaskService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 定时任务服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Service
public class ScheduledTaskServiceImpl extends ServiceImpl<ScheduledTaskConfigMapper, ScheduledTaskConfig> implements ScheduledTaskService {

    @Override
    public List<ScheduledTaskConfig> getEnabledTasks() {
        LambdaQueryWrapper<ScheduledTaskConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScheduledTaskConfig::getTaskStatus, TaskStatusEnum.ENABLED.getCode())
                   .eq(ScheduledTaskConfig::getDeleted, false);
        return list(queryWrapper);
    }

    @Override
    public ScheduledTaskConfig getByTaskCode(String taskCode) {
        return this.baseMapper.selectByTaskCode(taskCode);
    }

    @Override
    public boolean enableTask(String taskCode, String updater) {
        try {
            int result = this.baseMapper.updateTaskStatus(taskCode, TaskStatusEnum.ENABLED.getCode(), updater);
            return result > 0;
        } catch (Exception e) {
            log.error("启用任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean disableTask(String taskCode, String updater) {
        try {
            int result = this.baseMapper.updateTaskStatus(taskCode, TaskStatusEnum.DISABLED.getCode(), updater);
            return result > 0;
        } catch (Exception e) {
            log.error("禁用任务失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTaskParams(String taskCode, String taskParams, String updater) {
        try {
            int result = this.baseMapper.updateTaskParams(taskCode, taskParams, updater);
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务参数失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTaskRate(String taskCode, Long fixedRate, String updater) {
        try {
            int result = this.baseMapper.updateTaskRate(taskCode, fixedRate, updater);
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务频率失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTaskCron(String taskCode, String cronExpression, String updater) {
        try {
            int result = this.baseMapper.updateTaskCron(taskCode, cronExpression, updater);
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务Cron表达式失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void recordTaskStart(String taskCode) {
        try {
            this.baseMapper.updateTaskExecuteInfo(taskCode, "RUNNING", "任务开始执行");
            log.debug("记录任务开始执行，任务编码: {}", taskCode);
        } catch (Exception e) {
            log.error("记录任务开始执行失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
        }
    }

    @Override
    public void recordTaskSuccess(String taskCode, String message) {
        try {
            this.baseMapper.updateTaskExecuteInfo(taskCode, "SUCCESS", message);
            log.debug("记录任务执行成功，任务编码: {}, 消息: {}", taskCode, message);
        } catch (Exception e) {
            log.error("记录任务执行成功失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
        }
    }

    @Override
    public void recordTaskFailure(String taskCode, String message) {
        try {
            this.baseMapper.updateTaskExecuteInfo(taskCode, "FAILURE", message);
            log.debug("记录任务执行失败，任务编码: {}, 消息: {}", taskCode, message);
        } catch (Exception e) {
            log.error("记录任务执行失败失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);
        }
    }

    @Override
    public Long calculateNextExecuteTime(ScheduledTaskConfig taskConfig) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime nextExecuteTime;

            if (taskConfig.getFixedRate() != null && taskConfig.getFixedRate() > 0) {
                // 使用固定频率计算下次执行时间
                long intervalMillis = taskConfig.getFixedRate();
                nextExecuteTime = now.plusNanos(intervalMillis * 1_000_000);
            } else if (taskConfig.getFixedDelay() != null && taskConfig.getFixedDelay() > 0) {
                // 使用固定延迟计算下次执行时间
                long delayMillis = taskConfig.getFixedDelay();
                nextExecuteTime = now.plusNanos(delayMillis * 1_000_000);
            } else {
                // 默认1小时后执行
                nextExecuteTime = now.plusHours(1);
            }

            return nextExecuteTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("计算下次执行时间失败，任务编码: {}, 错误: {}", taskConfig.getTaskCode(), e.getMessage(), e);
            // 默认返回1小时后的时间
            return System.currentTimeMillis() + 3600000L;
        }
    }
}
