package plus.qdt.modules.system.task;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.client.YzhMessageClient;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;
import plus.qdt.modules.system.entity.enums.TaskStatusEnum;
import plus.qdt.modules.system.service.ScheduledTaskService;

/**
 * 云中鹤消息定时任务
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class YzhMessageScheduledTask {

    private final ScheduledTaskService scheduledTaskService;
    private final YzhMessageClient yzhMessageClient;

    /**
     * 云中鹤商品消息查询定时任务
     * 每10分钟执行一次（可通过数据库配置动态调整）
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void queryGoodsMessage() {
        executeTask("YZH_GOODS_MESSAGE_QUERY");
    }

    /**
     * 云中鹤销售订单消息查询定时任务
     * 每10分钟执行一次（可通过数据库配置动态调整）
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void querySalesOrderMessage() {
        executeTask("YZH_SALES_ORDER_MESSAGE_QUERY");
    }

    /**
     * 云中鹤售后订单消息查询定时任务
     * 每10分钟执行一次（可通过数据库配置动态调整）
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void queryAfterSaleMessage() {
        executeTask("YZH_AFTER_SALE_MESSAGE_QUERY");
    }

    /**
     * 执行定时任务
     *
     * @param taskCode 任务编码
     */
    private void executeTask(String taskCode) {
        try {
            // 1. 获取任务配置
            ScheduledTaskConfig taskConfig = scheduledTaskService.getByTaskCode(taskCode);
            if (taskConfig == null) {
                log.warn("任务配置不存在，任务编码: {}", taskCode);
                return;
            }

            // 2. 检查任务是否启用
            if (!TaskStatusEnum.ENABLED.getCode().equals(taskConfig.getTaskStatus())) {
                log.debug("任务未启用，跳过执行，任务编码: {}, 状态: {}", taskCode, taskConfig.getTaskStatus());
                return;
            }

            log.info("开始执行定时任务，任务编码: {}, 任务名称: {}", taskCode, taskConfig.getTaskName());

            // 3. 记录任务开始执行
            scheduledTaskService.recordTaskStart(taskCode);

            // 4. 解析任务参数
            YzhQueryMsgListRequestParam requestParam = parseTaskParams(taskConfig.getTaskParams());
            if (requestParam == null) {
                throw new RuntimeException("任务参数解析失败");
            }

            // 5. 通过Feign调用order-service执行任务
            ResultMessage<YzhQueryMsgListResponse> result = yzhMessageClient.queryMessageList(requestParam);
            
            if (!result.isSuccess()) {
                throw new RuntimeException("调用云中鹤消息服务失败: " + result.getMessage());
            }

            YzhQueryMsgListResponse response = result.getResult();

            // 6. 记录任务执行成功
            String successMessage = String.format("任务执行成功，查询到 %d 条消息",
                    response.getResult() != null ? response.getResult().getTotalCount() : 0);
            scheduledTaskService.recordTaskSuccess(taskCode, successMessage);

            log.info("定时任务执行完成，任务编码: {}, 结果: {}", taskCode, successMessage);

        } catch (Exception e) {
            log.error("定时任务执行失败，任务编码: {}, 错误: {}", taskCode, e.getMessage(), e);

            // 7. 记录任务执行失败
            String errorMessage = String.format("任务执行失败: %s", e.getMessage());
            scheduledTaskService.recordTaskFailure(taskCode, errorMessage);
        }
    }

    /**
     * 解析任务参数
     *
     * @param taskParamsJson 任务参数JSON字符串
     * @return 请求参数对象
     */
    private YzhQueryMsgListRequestParam parseTaskParams(String taskParamsJson) {
        try {
            if (taskParamsJson == null || taskParamsJson.trim().isEmpty()) {
                log.warn("任务参数为空，使用默认参数");
                return YzhQueryMsgListRequestParam.builder()
                        .messageType(1) // 默认查询商品消息
                        .pageNum(1)
                        .pageSize(25)
                        .build();
            }

            JSONObject paramsJson = JSONObject.parseObject(taskParamsJson);
            return YzhQueryMsgListRequestParam.builder()
                    .messageType(paramsJson.getInteger("messageType"))
                    .pageNum(paramsJson.getInteger("pageNum"))
                    .pageSize(paramsJson.getInteger("pageSize"))
                    .build();

        } catch (Exception e) {
            log.error("解析任务参数失败，参数: {}, 错误: {}", taskParamsJson, e.getMessage(), e);
            return null;
        }
    }
}
