package plus.qdt.modules.system.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import plus.qdt.modules.order.order.entity.dos.Receipt;
import plus.qdt.modules.system.entity.MessageRecord;
import plus.qdt.modules.system.entity.enums.MessageStatusEnum;
import plus.qdt.modules.system.entity.enums.TaskStatusEnum;
import plus.qdt.modules.system.service.MessageRecordService;
import plus.qdt.modules.system.service.ScheduledTaskService;
import plus.qdt.modules.system.entity.dos.ScheduledTaskConfig;
import plus.qdt.routing.ReceiptRoutingKey;

import java.util.Date;
import java.util.List;

/**
 * 发票消息补偿定时任务
 * 每1小时执行一次，检查MessageRecord里的发票业务是否有没有消费的记录，
 * 如果有则重发该条消息到ReceiptMessageListener
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReceiptMessageCompensationTask {

    private final MessageRecordService messageRecordService;
    private final ScheduledTaskService scheduledTaskService;
    private final RabbitTemplate rabbitTemplate;

    @Value("${qdt.amqp.receipt:receipt}")
    private String receiptExchange;

    /**
     * 发票消息补偿任务
     * 每1小时执行一次（可通过数据库配置动态调整）
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void compensateReceiptMessage() {
        executeTask("RECEIPT_MESSAGE_COMPENSATION");
    }

    /**
     * 执行发票消息补偿任务
     *
     * @param taskCode 任务编码
     */
    private void executeTask(String taskCode) {
        try {
            // 1. 获取任务配置
            ScheduledTaskConfig taskConfig = scheduledTaskService.getByTaskCode(taskCode);
            if (taskConfig == null) {
                log.warn("任务配置不存在，任务编码: {}", taskCode);
                return;
            }

            // 2. 检查任务是否启用
            if (!TaskStatusEnum.ENABLED.getCode().equals(taskConfig.getTaskStatus())) {
                log.debug("任务未启用，跳过执行，任务编码: {}, 状态: {}", taskCode, taskConfig.getTaskStatus());
                return;
            }

            log.info("开始执行发票消息补偿任务，任务编码: {}, 任务名称: {}", taskCode, taskConfig.getTaskName());

            // 3. 记录任务开始执行
            scheduledTaskService.recordTaskStart(taskCode);

            // 4. 查询需要补偿的发票消息记录
            List<MessageRecord> pendingMessages = findPendingReceiptMessages();

            if (pendingMessages.isEmpty()) {
                String successMessage = "发票消息补偿任务执行完成，无需要补偿的消息";
                scheduledTaskService.recordTaskSuccess(taskCode, successMessage);
                log.info(successMessage);
                return;
            }

            // 5. 处理需要补偿的消息
            int successCount = 0;
            int failCount = 0;

            for (MessageRecord messageRecord : pendingMessages) {
                try {
                    // 重发消息到ReceiptMessageListener
                    boolean resendResult = resendReceiptMessage(messageRecord);
                    
                    if (resendResult) {
                        // 更新消息状态为已发送
                        messageRecordService.updateMessageStatus(messageRecord.getId(), 
                                MessageStatusEnum.SENT.getCode(), null);
                        successCount++;
                        log.info("发票消息重发成功，消息ID: {}, 业务键: {}", 
                                messageRecord.getMessageId(), messageRecord.getBizKey());
                    } else {
                        // 增加重试次数
                        messageRecordService.incrementRetryCount(messageRecord.getId());
                        failCount++;
                        log.warn("发票消息重发失败，消息ID: {}, 业务键: {}", 
                                messageRecord.getMessageId(), messageRecord.getBizKey());
                    }
                } catch (Exception e) {
                    // 记录错误并增加重试次数
                    String errorMsg = "重发消息异常: " + e.getMessage();
                    messageRecordService.updateMessageStatus(messageRecord.getId(), 
                            MessageStatusEnum.FAILED.getCode(), errorMsg);
                    messageRecordService.incrementRetryCount(messageRecord.getId());
                    failCount++;
                    log.error("发票消息重发异常，消息ID: {}, 业务键: {}, 错误: {}", 
                            messageRecord.getMessageId(), messageRecord.getBizKey(), e.getMessage(), e);
                }
            }

            // 6. 记录任务执行结果
            String resultMessage = String.format("发票消息补偿任务执行完成，处理消息数: %d, 成功: %d, 失败: %d", 
                    pendingMessages.size(), successCount, failCount);
            scheduledTaskService.recordTaskSuccess(taskCode, resultMessage);
            log.info(resultMessage);

        } catch (Exception e) {
            String errorMessage = "发票消息补偿任务执行异常: " + e.getMessage();
            scheduledTaskService.recordTaskFailure(taskCode, errorMessage);
            log.error("发票消息补偿任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询需要补偿的发票消息记录
     * 查询条件：
     * 1. 业务类型为发票相关（RECEIPT_CREATE）
     * 2. 消息状态为待发送或发送失败
     * 3. 重试次数小于最大重试次数
     * 4. 创建时间在合理范围内（避免处理过旧的消息）
     *
     * @return 需要补偿的消息记录列表
     */
    private List<MessageRecord> findPendingReceiptMessages() {
        LambdaQueryWrapper<MessageRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        // 业务类型为发票创建
        queryWrapper.eq(MessageRecord::getBizType, "RECEIPT_CREATE");
        
        // 消息状态为待发送或发送失败
        queryWrapper.in(MessageRecord::getMessageStatus, 
                MessageStatusEnum.PENDING.getCode(), MessageStatusEnum.FAILED.getCode());
        
        // 重试次数小于最大重试次数
        queryWrapper.lt(MessageRecord::getRetryCount, MessageRecord::getMaxRetry);
        
        // 创建时间在24小时内（避免处理过旧的消息）
        Date oneDayAgo = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000);
        queryWrapper.ge(MessageRecord::getCreatedAt, oneDayAgo);
        
        // 按创建时间升序排列
        queryWrapper.orderByAsc(MessageRecord::getCreatedAt);
        
        return messageRecordService.list(queryWrapper);
    }

    /**
     * 重发发票消息到ReceiptMessageListener
     *
     * @param messageRecord 消息记录
     * @return 是否重发成功
     */
    private boolean resendReceiptMessage(MessageRecord messageRecord) {
        try {
            // 解析消息内容为Receipt对象
            Receipt receipt = JSONUtil.toBean(messageRecord.getMessageContent(), Receipt.class);
            
            // 将Receipt对象转换为JSON字符串
            String receiptMessageJson = JSONUtil.toJsonStr(receipt);
            
            // 发送消息到发票队列
            String routingKey = ReceiptRoutingKey.RECEIPT_CREATE;

            rabbitTemplate.convertAndSend(receiptExchange, routingKey, receiptMessageJson);
            
            log.debug("发票消息重发成功，交换机: {}, 路由键: {}, 消息ID: {}",
                    receiptExchange, routingKey, messageRecord.getMessageId());
            
            return true;
        } catch (Exception e) {
            log.error("重发发票消息失败，消息ID: {}, 错误: {}", 
                    messageRecord.getMessageId(), e.getMessage(), e);
            return false;
        }
    }
}
