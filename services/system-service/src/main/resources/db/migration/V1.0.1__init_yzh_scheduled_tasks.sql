-- 初始化云中鹤定时任务配置
-- 如果表不存在则创建
CREATE TABLE IF NOT EXISTS `scheduled_task_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_code` varchar(50) NOT NULL COMMENT '任务编码（唯一标识）',
  `task_description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `task_type` varchar(50) DEFAULT NULL COMMENT '任务类型',
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Cron表达式',
  `fixed_rate` bigint DEFAULT NULL COMMENT '固定频率（毫秒）',
  `fixed_delay` bigint DEFAULT NULL COMMENT '固定延迟（毫秒）',
  `task_status` varchar(20) NOT NULL DEFAULT 'DISABLED' COMMENT '任务状态',
  `task_params` text COMMENT '任务参数（JSON格式）',
  `last_execute_time` datetime DEFAULT NULL COMMENT '上次执行时间',
  `next_execute_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `execute_count` bigint DEFAULT 0 COMMENT '执行次数',
  `success_count` bigint DEFAULT 0 COMMENT '成功次数',
  `fail_count` bigint DEFAULT 0 COMMENT '失败次数',
  `last_execute_result` varchar(20) DEFAULT NULL COMMENT '上次执行结果',
  `last_execute_message` text COMMENT '上次执行消息',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code` (`task_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务配置表';

-- 插入云中鹤定时任务配置（如果不存在）
INSERT IGNORE INTO `scheduled_task_config` 
(`task_name`, `task_code`, `task_description`, `task_type`, `cron_expression`, `fixed_rate`, `task_status`, `task_params`, `creator`) 
VALUES 
('云中鹤商品消息查询', 'YZH_GOODS_MESSAGE_QUERY', '定时查询云中鹤商品相关消息', 'YZH_MESSAGE', '0 */10 * * * ?', 600000, 'ENABLED', '{"messageType":1,"pageSize":100,"pageNum":1}', 'system'),
('云中鹤销售订单消息查询', 'YZH_SALES_ORDER_MESSAGE_QUERY', '定时查询云中鹤销售订单相关消息', 'YZH_MESSAGE', '0 */10 * * * ?', 600000, 'ENABLED', '{"messageType":2,"pageSize":100,"pageNum":1}', 'system'),
('云中鹤售后订单消息查询', 'YZH_AFTER_SALE_MESSAGE_QUERY', '定时查询云中鹤售后订单相关消息', 'YZH_MESSAGE', '0 */10 * * * ?', 600000, 'ENABLED', '{"messageType":3,"pageSize":100,"pageNum":1}', 'system'),
('发票消息补偿任务', 'RECEIPT_MESSAGE_COMPENSATION', '定时补偿发送失败的发票消息', 'MESSAGE_COMPENSATION', '0 0 */1 * * ?', 3600000, 'DISABLED', '{"bizType":"RECEIPT_CREATE","maxRetryHours":24,"batchSize":100}', 'system');
