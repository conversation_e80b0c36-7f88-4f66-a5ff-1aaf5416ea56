package plus.qdt.modules.goods.serviceimpl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.common.utils.CategorySimilarityUtils;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.modules.goods.entity.dos.YzhCategoryMapping;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO;
import plus.qdt.modules.goods.entity.dto.YzhCategoryMappingSearchParams;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.goods.entity.vos.CategoryVO;
import plus.qdt.modules.goods.entity.vos.YzhCategoryMappingSupplierVO;
import plus.qdt.modules.goods.mapper.YzhCategoryMappingMapper;
import plus.qdt.modules.goods.service.YzhCategoryMappingService;
import plus.qdt.mybatis.util.PageUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云中鹤分类映射服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YzhCategoryMappingServiceImpl extends ServiceImpl<YzhCategoryMappingMapper, YzhCategoryMapping> 
        implements YzhCategoryMappingService {

    private final YzhCategoryMappingMapper mapper;

    @Override
    @Transactional
    public boolean processCategoryMapping(List<YZHGoodsSkuDTO> goodsDetailList, List<CategoryVO> categoryVOS) {
        log.info("开始处理分类映射，商品数量: {}", goodsDetailList.size());

        try {
            int successCount = 0;
            int failCount = 0;
            int skipCount = 0;

            for (YZHGoodsSkuDTO goodsDetail : goodsDetailList) {
                try {
                    // 处理单个商品分类映射
                    YzhCategoryMappingDTO mapping = createCategoryMapping(goodsDetail, categoryVOS);
                    if (mapping != null) {
                        successCount++;
                    } else {
                        skipCount++;
                        log.debug("跳过处理商品分类映射，SKU: {} (分类数据无效)", goodsDetail.getGoodsSkuCode());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("处理商品分类映射失败，SKU: {}, 错误: {}",
                             goodsDetail.getGoodsSkuCode(), e.getMessage(), e);
                }
            }

            log.info("分类映射处理完成，成功: {} 条，跳过: {} 条，失败: {} 条", successCount, skipCount, failCount);
            return failCount == 0;

        } catch (Exception e) {
            log.error("批量处理分类映射时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public YzhCategoryMappingDTO createCategoryMapping(YZHGoodsSkuDTO goodsDetail, List<CategoryVO> categoryVOS) {
        // 验证云中鹤分类数据是否为空
        if (!isValidYzhCategoryData(goodsDetail)) {
            log.warn("云中鹤分类数据为空，跳过匹配，SKU: {}", goodsDetail.getGoodsSkuCode());
            return null;
        }
        
        // 创建云中鹤分类信息
        YzhCategoryMappingDTO.YzhCategoryInfo yzhCategory = new YzhCategoryMappingDTO.YzhCategoryInfo();
        yzhCategory.setFirstCategoryCode(goodsDetail.getFirstCategoryCode());
        yzhCategory.setFirstCategoryName(goodsDetail.getFirstCategoryName());
        yzhCategory.setSecondCategoryCode(goodsDetail.getSecondCategoryCode());
        yzhCategory.setSecondCategoryName(goodsDetail.getSecondCategoryName());
        yzhCategory.setLastCategoryCode(goodsDetail.getLastCategoryCode());
        yzhCategory.setLastCategoryName(goodsDetail.getLastCategoryName());
        
        // 自动匹配分类
        YzhCategoryMappingDTO.CategoryMatchResult matchResult = autoMatchCategory(yzhCategory, categoryVOS);

        log.info("分类匹配结果：hasMatch={}, bestMatch={}",
                matchResult.getHasMatch(),
                matchResult.getBestMatch() != null ? matchResult.getBestMatch().getCategoryName() : "null");

        // 创建映射记录
        YzhCategoryMapping mapping = new YzhCategoryMapping();
        mapping.setId(SnowFlake.getIdStr());
        mapping.setYzhFirstCategoryCode(goodsDetail.getFirstCategoryCode());
        mapping.setYzhFirstCategoryName(goodsDetail.getFirstCategoryName());
        mapping.setYzhSecondCategoryCode(goodsDetail.getSecondCategoryCode());
        mapping.setYzhSecondCategoryName(goodsDetail.getSecondCategoryName());
        mapping.setYzhLastCategoryCode(goodsDetail.getLastCategoryCode());
        mapping.setYzhLastCategoryName(goodsDetail.getLastCategoryName());
        mapping.setGoodsSkuCode(goodsDetail.getGoodsSkuCode());
        mapping.setGoodsCount(1);
        mapping.setMappingType(YzhCategoryMapping.MappingType.AUTO.getCode());
        
        if (matchResult.getBestMatch() != null) {
            YzhCategoryMappingDTO.SystemCategoryMatch bestMatch = matchResult.getBestMatch();

            // 根据匹配层级设置系统分类信息（可能是部分匹配）
            setSystemCategoryInfo(mapping, bestMatch, categoryVOS);

            mapping.setSimilarityScore(bestMatch.getSimilarityScore());
            mapping.setMatchLevel(bestMatch.getLevel());

            // 关键：只有hasMatch为true（即三级分类匹配成功）才算真正成功
            if (matchResult.getHasMatch()) {
                // 三级分类匹配成功
                mapping.setMatchStatus(YzhCategoryMapping.MatchStatus.MATCHED.getCode());
                mapping.setAuditStatus(CategorySimilarityUtils.isHighSimilarity(bestMatch.getSimilarityScore()) ?
                        YzhCategoryMapping.AuditStatus.APPROVED.getCode() :
                        YzhCategoryMapping.AuditStatus.PENDING.getCode());

                log.info("三级分类匹配成功，整体状态：成功，匹配层级: {}，相似度: {}",
                        bestMatch.getLevel(), bestMatch.getSimilarityScore());
            } else {
                // 只有一级或二级分类匹配成功，整体算失败但保存匹配到的数据
                mapping.setMatchStatus(YzhCategoryMapping.MatchStatus.FAILED.getCode());
                mapping.setAuditStatus(YzhCategoryMapping.AuditStatus.PENDING.getCode());

                String matchedLevelName = bestMatch.getLevel() == 0 ? "一级" :
                                        bestMatch.getLevel() == 1 ? "二级" : "三级";
                log.warn("只有{}分类匹配成功，整体状态：失败（但已保存{}分类数据），匹配层级: {}，相似度: {}",
                        matchedLevelName, matchedLevelName, bestMatch.getLevel(), bestMatch.getSimilarityScore());
            }
        } else {
            // 完全没有匹配，系统分类信息置空
            mapping.setSystemFirstCategoryId(null);
            mapping.setSystemFirstCategoryName(null);
            mapping.setSystemSecondCategoryId(null);
            mapping.setSystemSecondCategoryName(null);
            mapping.setSystemLastCategoryId(null);
            mapping.setSystemLastCategoryName(null);

            mapping.setSimilarityScore(null);
            mapping.setMatchLevel(null);
            mapping.setMatchStatus(YzhCategoryMapping.MatchStatus.FAILED.getCode());
            mapping.setAuditStatus(YzhCategoryMapping.AuditStatus.PENDING.getCode());

            log.warn("所有层级分类匹配均失败，系统分类信息已置空，等待手动匹配。云中鹤分类：一级[{}]，二级[{}]，三级[{}]",
                    goodsDetail.getFirstCategoryName(),
                    goodsDetail.getSecondCategoryName(),
                    goodsDetail.getLastCategoryName());
        }

        // 检查是否已存在完全相同的映射（包括系统分类也相同）
        // 注意：如果匹配失败，系统分类为空，仍然需要检查重复
        YzhCategoryMapping duplicateMapping = findCompleteMapping(goodsDetail, mapping);
        if (duplicateMapping != null) {
            log.info("发现完全相同的映射，更新商品数量，SKU: {}, 映射ID: {}, 匹配状态: {}",
                     goodsDetail.getGoodsSkuCode(), duplicateMapping.getId(), duplicateMapping.getMatchStatus());
            // 更新商品数量
            duplicateMapping.setGoodsCount(duplicateMapping.getGoodsCount() + 1);
            this.updateById(duplicateMapping);
            return convertToDTO(duplicateMapping);
        } else {
            log.info("未发现重复映射，准备创建新的映射记录，SKU: {}", goodsDetail.getGoodsSkuCode());
        }

        mapping.setPlatformCode(SupplierEnum.YZH.name());
        mapping.setPlatformName(SupplierEnum.YZH.getDescription());
        this.save(mapping);
        log.info("创建分类映射成功，SKU: {}, 匹配状态: {}, 系统分类: 一级[{}], 二级[{}], 三级[{}]",
                 goodsDetail.getGoodsSkuCode(),
                 mapping.getMatchStatus(),
                 mapping.getSystemFirstCategoryName(),
                 mapping.getSystemSecondCategoryName(),
                 mapping.getSystemLastCategoryName());

        return convertToDTO(mapping);
    }

    @Override
    public YzhCategoryMappingDTO.CategoryMatchResult autoMatchCategory(
            YzhCategoryMappingDTO.YzhCategoryInfo yzhCategory, List<CategoryVO> categoryVOS) {
        
        YzhCategoryMappingDTO.CategoryMatchResult result = new YzhCategoryMappingDTO.CategoryMatchResult();
        result.setYzhCategory(yzhCategory);
        result.setSystemMatches(new ArrayList<>());
        result.setHasMatch(false);
        
        List<YzhCategoryMappingDTO.SystemCategoryMatch> allMatches = new ArrayList<>();
        
        // 获取所有分类的扁平列表
        List<CategoryVO> flatCategories = flattenCategories(categoryVOS);

        log.info("开始分层级匹配云中鹤分类，云中鹤分类信息：一级[{}]，二级[{}]，三级[{}]",
                yzhCategory.getFirstCategoryName(),
                yzhCategory.getSecondCategoryName(),
                yzhCategory.getLastCategoryName());

        // 分层级匹配，但只有三级分类匹配成功才算真正成功
        YzhCategoryMappingDTO.SystemCategoryMatch thirdLevelMatch = null;
        YzhCategoryMappingDTO.SystemCategoryMatch secondLevelMatch = null;
        YzhCategoryMappingDTO.SystemCategoryMatch firstLevelMatch = null;

        // 1. 尝试匹配三级分类（需要同时有编码和名称）
        if (StrUtil.isNotBlank(yzhCategory.getLastCategoryCode()) &&
            StrUtil.isNotBlank(yzhCategory.getLastCategoryName())) {
            thirdLevelMatch = matchCategoryByLevel(yzhCategory.getLastCategoryName(), flatCategories, 2, "三级分类");
        } else {
            log.debug("三级分类数据不完整，跳过三级分类匹配，编码: [{}], 名称: [{}]",
                     yzhCategory.getLastCategoryCode(), yzhCategory.getLastCategoryName());
        }

        // 2. 尝试匹配二级分类（需要同时有编码和名称）
        if (StrUtil.isNotBlank(yzhCategory.getSecondCategoryCode()) &&
            StrUtil.isNotBlank(yzhCategory.getSecondCategoryName())) {
            secondLevelMatch = matchCategoryByLevel(yzhCategory.getSecondCategoryName(), flatCategories, 1, "二级分类");
        } else {
            log.debug("二级分类数据不完整，跳过二级分类匹配，编码: [{}], 名称: [{}]",
                     yzhCategory.getSecondCategoryCode(), yzhCategory.getSecondCategoryName());
        }

        // 3. 尝试匹配一级分类（需要同时有编码和名称）
        if (StrUtil.isNotBlank(yzhCategory.getFirstCategoryCode()) &&
            StrUtil.isNotBlank(yzhCategory.getFirstCategoryName())) {
            firstLevelMatch = matchCategoryByLevel(yzhCategory.getFirstCategoryName(), flatCategories, 0, "一级分类");
        } else {
            log.debug("一级分类数据不完整，跳过一级分类匹配，编码: [{}], 名称: [{}]",
                     yzhCategory.getFirstCategoryCode(), yzhCategory.getFirstCategoryName());
        }

        // 确定最终的匹配结果和状态
        YzhCategoryMappingDTO.SystemCategoryMatch finalMatch = null;
        boolean isRealSuccess = false;

        if (thirdLevelMatch != null) {
            // 三级分类匹配成功，这是真正的成功
            finalMatch = thirdLevelMatch;
            isRealSuccess = true;
            log.info("三级分类匹配成功，整体匹配状态：成功");
        } else if (secondLevelMatch != null) {
            // 只有二级分类匹配成功，算作部分成功，保存二级分类数据
            finalMatch = secondLevelMatch;
            isRealSuccess = false;
            log.info("二级分类匹配成功，整体匹配状态：部分成功（保存一、二级分类数据）");
        } else if (firstLevelMatch != null) {
            // 只有一级分类匹配成功，算作部分成功，保存一级分类数据
            finalMatch = firstLevelMatch;
            isRealSuccess = false;
            log.info("一级分类匹配成功，整体匹配状态：部分成功（保存一级分类数据）");
        } else {
            // 所有层级都匹配失败
            log.warn("所有层级分类匹配均失败，整体匹配状态：失败");
        }

        // 设置匹配结果
        if (finalMatch != null) {
            allMatches.add(finalMatch);
            // 设置特殊标记，表示是否为真正的成功（只有三级匹配成功才是真正成功）
            result.setHasMatch(isRealSuccess);
        } else {
            result.setHasMatch(false);
        }
        
        if (!allMatches.isEmpty()) {
            // 按相似度排序，取最佳匹配
            allMatches.sort((a, b) -> b.getSimilarityScore().compareTo(a.getSimilarityScore()));

            result.setSystemMatches(allMatches);
            YzhCategoryMappingDTO.SystemCategoryMatch finalBestMatch = allMatches.get(0);
            result.setBestMatch(finalBestMatch);
            // 注意：这里不要重新设置hasMatch，应该保持之前设置的isRealSuccess值

            // 安全地设置匹配层级
            Integer matchLevel = finalBestMatch.getLevel();
            result.setMatchLevel(matchLevel != null ? matchLevel : 0);
        }
        
        return result;
    }

    @Override
    public YzhCategoryMapping findMappingByYzhCategory(String firstCategoryCode, String secondCategoryCode, String lastCategoryCode) {
        LambdaQueryWrapper<YzhCategoryMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YzhCategoryMapping::getYzhFirstCategoryCode, firstCategoryCode)
                .eq(YzhCategoryMapping::getYzhSecondCategoryCode, secondCategoryCode)
                .eq(YzhCategoryMapping::getYzhLastCategoryCode, lastCategoryCode)
                .eq(YzhCategoryMapping::getDeleteFlag, false)
                .orderByDesc(YzhCategoryMapping::getCreateTime)
                .last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    /**
     * 查找完全相同的映射（包括云中鹤分类和系统分类都相同）
     *
     * @param goodsDetail 商品详情
     * @param mapping 要保存的映射对象
     * @return 已存在的完全相同映射记录，如果不存在返回null
     */
    private YzhCategoryMapping findCompleteMapping(YZHGoodsSkuDTO goodsDetail, YzhCategoryMapping mapping) {
        LambdaQueryWrapper<YzhCategoryMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YzhCategoryMapping::getYzhFirstCategoryCode, goodsDetail.getFirstCategoryCode())
                .eq(YzhCategoryMapping::getYzhFirstCategoryName, goodsDetail.getFirstCategoryName())
                .eq(YzhCategoryMapping::getYzhSecondCategoryCode, goodsDetail.getSecondCategoryCode())
                .eq(YzhCategoryMapping::getYzhSecondCategoryName, goodsDetail.getSecondCategoryName())
                .eq(YzhCategoryMapping::getYzhLastCategoryCode, goodsDetail.getLastCategoryCode())
                .eq(YzhCategoryMapping::getYzhLastCategoryName, goodsDetail.getLastCategoryName())
                .eq(YzhCategoryMapping::getDeleteFlag, false);

        // 同时检查系统分类是否也相同
        if (mapping.getSystemFirstCategoryId() != null) {
            queryWrapper.eq(YzhCategoryMapping::getSystemFirstCategoryId, mapping.getSystemFirstCategoryId());
        } else {
            queryWrapper.isNull(YzhCategoryMapping::getSystemFirstCategoryId);
        }

        if (mapping.getSystemSecondCategoryId() != null) {
            queryWrapper.eq(YzhCategoryMapping::getSystemSecondCategoryId, mapping.getSystemSecondCategoryId());
        } else {
            queryWrapper.isNull(YzhCategoryMapping::getSystemSecondCategoryId);
        }

        if (mapping.getSystemLastCategoryId() != null) {
            queryWrapper.eq(YzhCategoryMapping::getSystemLastCategoryId, mapping.getSystemLastCategoryId());
        } else {
            queryWrapper.isNull(YzhCategoryMapping::getSystemLastCategoryId);
        }

        queryWrapper.last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    /**
     * 按指定层级匹配分类
     *
     * @param yzhCategoryName 云中鹤分类名称
     * @param flatCategories 扁平化的系统分类列表
     * @param targetLevel 目标层级（0-一级，1-二级，2-三级）
     * @param levelName 层级名称（用于日志）
     * @return 匹配结果，如果没有匹配返回null
     */
    private YzhCategoryMappingDTO.SystemCategoryMatch matchCategoryByLevel(
            String yzhCategoryName, List<CategoryVO> flatCategories, int targetLevel, String levelName) {

        log.info("开始{}匹配，云中鹤分类名称: {}", levelName, yzhCategoryName);

        // 筛选指定层级的分类
        List<CategoryVO> targetLevelCategories = flatCategories.stream()
                .filter(category -> {
                    Integer level = category.getLevel();
                    if (level == null) {
                        level = inferCategoryLevel(category, flatCategories);
                    }
                    return level != null && level == targetLevel;
                })
                .collect(Collectors.toList());

        log.info("找到 {} 个系统{}进行匹配", targetLevelCategories.size(), levelName);

        YzhCategoryMappingDTO.SystemCategoryMatch bestMatch = null;
        BigDecimal bestSimilarity = BigDecimal.ZERO;

        // 对指定层级的分类进行匹配
        for (CategoryVO category : targetLevelCategories) {
            BigDecimal similarity = CategorySimilarityUtils.calculateSimilarity(yzhCategoryName, category.getName());

            if (CategorySimilarityUtils.isSimilar(similarity)) {
                log.debug("找到候选匹配: {} (相似度: {})", category.getName(), similarity);

                // 选择相似度最高的
                if (similarity.compareTo(bestSimilarity) > 0) {
                    bestSimilarity = similarity;

                    bestMatch = new YzhCategoryMappingDTO.SystemCategoryMatch();
                    bestMatch.setCategoryId(category.getId());
                    bestMatch.setCategoryName(category.getName());
                    bestMatch.setLevel(targetLevel);
                    bestMatch.setSimilarityScore(similarity);
                    bestMatch.setMatchedField(yzhCategoryName);
                    bestMatch.setParentId(category.getParentId());
                    bestMatch.setFullPath(buildCategoryPath(category, flatCategories));
                }
            }
        }

        if (bestMatch != null) {
            log.info("{}匹配成功: {} -> {} (相似度: {})",
                    levelName, yzhCategoryName, bestMatch.getCategoryName(), bestSimilarity);
        } else {
            log.info("{}匹配失败: {}", levelName, yzhCategoryName);
        }

        return bestMatch;
    }

    /**
     * 将分类树扁平化
     */
    private List<CategoryVO> flattenCategories(List<CategoryVO> categoryVOS) {
        List<CategoryVO> result = new ArrayList<>();
        for (CategoryVO category : categoryVOS) {
            result.add(category);
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                result.addAll(flattenCategories(category.getChildren()));
            }
        }
        return result;
    }

    /**
     * 推断分类层级
     *
     * @param category 分类对象
     * @param categoryVOS 分类树
     * @return 推断的层级，0表示一级分类，1表示二级分类，2表示三级分类
     */
    private Integer inferCategoryLevel(CategoryVO category, List<CategoryVO> categoryVOS) {
        // 如果parentId为null或"0"，则为一级分类
        String parentId = category.getParentId();
        if (parentId == null || "0".equals(parentId) || parentId.isEmpty()) {
            return 0;
        }

        // 查找父分类
        CategoryVO parentCategory = findCategoryById(parentId, categoryVOS);
        if (parentCategory == null) {
            // 找不到父分类，默认为一级分类
            return 0;
        }

        // 递归计算层级
        Integer parentLevel = parentCategory.getLevel();
        if (parentLevel == null) {
            parentLevel = inferCategoryLevel(parentCategory, categoryVOS);
        }

        return parentLevel + 1;
    }

    /**
     * 根据ID查找分类
     *
     * @param categoryId 分类ID
     * @param categoryVOS 分类树
     * @return 找到的分类，如果没找到返回null
     */
    private CategoryVO findCategoryById(String categoryId, List<CategoryVO> categoryVOS) {
        for (CategoryVO category : categoryVOS) {
            if (categoryId.equals(category.getId())) {
                return category;
            }
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                CategoryVO found = findCategoryById(categoryId, category.getChildren());
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * 构建分类路径
     */
    private String buildCategoryPath(CategoryVO category, List<CategoryVO> categoryVOS) {
        // TODO: 实现分类路径构建逻辑
        return category.getName();
    }

    /**
     * 设置系统分类信息（根据匹配层级设置对应的分类信息）
     */
    @Override
    public void setSystemCategoryInfo(YzhCategoryMapping mapping,
                                     YzhCategoryMappingDTO.SystemCategoryMatch match,
                                     List<CategoryVO> categoryVOS) {
        // 检查level是否为null
        Integer level = match.getLevel();
        if (level == null) {
            log.warn("匹配的分类层级为null，跳过设置系统分类信息，分类ID: {}, 分类名称: {}",
                     match.getCategoryId(), match.getCategoryName());
            return;
        }

        // 找到匹配的分类
        CategoryVO matchedCategory = findCategoryById(match.getCategoryId(), categoryVOS);
        if (matchedCategory == null) {
            log.warn("找不到匹配的分类，分类ID: {}, 分类名称: {}",
                     match.getCategoryId(), match.getCategoryName());
            return;
        }

        if (level == 2) {
            // 三级分类匹配成功，设置完整的分类层级链
            List<CategoryVO> categoryChain = buildCategoryChain(matchedCategory, categoryVOS);
            setCategoryChainToMapping(mapping, categoryChain);
            log.info("三级分类匹配成功，已设置完整分类链");
        } else if (level == 1) {
            // 二级分类匹配成功，只设置一级和二级分类
            List<CategoryVO> categoryChain = buildCategoryChain(matchedCategory, categoryVOS);
            setPartialCategoryChainToMapping(mapping, categoryChain, 2); // 只设置前2级
            log.info("二级分类匹配成功，已设置一级和二级分类信息");
        } else if (level == 0) {
            // 一级分类匹配成功，只设置一级分类
            mapping.setSystemFirstCategoryId(matchedCategory.getId());
            mapping.setSystemFirstCategoryName(matchedCategory.getName());
            log.info("一级分类匹配成功，已设置一级分类信息: {} - {}",
                    matchedCategory.getId(), matchedCategory.getName());
        }

        log.debug("设置系统分类信息完成，匹配分类: {} (层级{})",
                 match.getCategoryName(), level);
    }

    /**
     * 设置部分分类链到映射对象中
     *
     * @param mapping 映射对象
     * @param categoryChain 分类链
     * @param maxLevel 最大层级（1=只设置一级，2=设置一级和二级，3=设置全部）
     */
    private void setPartialCategoryChainToMapping(YzhCategoryMapping mapping, List<CategoryVO> categoryChain, int maxLevel) {
        if (categoryChain.isEmpty()) {
            log.warn("分类链为空，无法设置分类信息");
            return;
        }

        log.info("开始设置部分分类链到映射对象，分类链长度: {}，最大层级: {}", categoryChain.size(), maxLevel);

        // 设置一级分类
        if (maxLevel >= 1 && categoryChain.size() >= 1) {
            CategoryVO firstCategory = categoryChain.get(0);
            mapping.setSystemFirstCategoryId(firstCategory.getId());
            mapping.setSystemFirstCategoryName(firstCategory.getName());
            log.info("✓ 设置一级分类: {} - {}", firstCategory.getId(), firstCategory.getName());
        }

        // 设置二级分类
        if (maxLevel >= 2 && categoryChain.size() >= 2) {
            CategoryVO secondCategory = categoryChain.get(1);
            mapping.setSystemSecondCategoryId(secondCategory.getId());
            mapping.setSystemSecondCategoryName(secondCategory.getName());
            log.info("✓ 设置二级分类: {} - {}", secondCategory.getId(), secondCategory.getName());
        }

        // 设置三级分类
        if (maxLevel >= 3 && categoryChain.size() >= 3) {
            CategoryVO thirdCategory = categoryChain.get(2);
            mapping.setSystemLastCategoryId(thirdCategory.getId());
            mapping.setSystemLastCategoryName(thirdCategory.getName());
            log.info("✓ 设置三级分类: {} - {}", thirdCategory.getId(), thirdCategory.getName());
        }

        // 输出最终设置结果
        log.info("部分分类链设置完成 - 一级: {}({}), 二级: {}({}), 三级: {}({})",
                mapping.getSystemFirstCategoryName(), mapping.getSystemFirstCategoryId(),
                mapping.getSystemSecondCategoryName(), mapping.getSystemSecondCategoryId(),
                mapping.getSystemLastCategoryName(), mapping.getSystemLastCategoryId());
    }

    /**
     * 构建分类层级链（从根分类到当前分类）
     */
    private List<CategoryVO> buildCategoryChain(CategoryVO category, List<CategoryVO> categoryVOS) {
        List<CategoryVO> chain = new ArrayList<>();
        CategoryVO current = category;
        int depth = 0;
        final int MAX_DEPTH = 10; // 防止无限循环

        log.debug("开始构建分类链，起始分类: {} (ID: {}, ParentID: {})",
                 current.getName(), current.getId(), current.getParentId());

        // 从当前分类向上追溯到根分类
        while (current != null && depth < MAX_DEPTH) {
            chain.add(0, current); // 插入到列表开头，保证顺序是从根到叶子
            log.debug("添加分类到链中: {} (ID: {}, ParentID: {}), 当前链长度: {}",
                     current.getName(), current.getId(), current.getParentId(), chain.size());

            String parentId = current.getParentId();
            if (parentId == null || "0".equals(parentId) || parentId.isEmpty()) {
                log.debug("到达根分类，停止追溯");
                break; // 到达根分类
            }

            CategoryVO parent = findCategoryById(parentId, categoryVOS);
            if (parent == null) {
                log.warn("找不到父分类，ParentID: {}，停止追溯", parentId);
                break;
            }

            current = parent;
            depth++;
        }

        if (depth >= MAX_DEPTH) {
            log.error("分类链构建超过最大深度限制，可能存在循环引用，起始分类: {}", category.getName());
        }

        log.debug("分类链构建完成，总长度: {}，链路: {}", chain.size(),
                 chain.stream().map(c -> c.getName() + "(" + c.getId() + ")").collect(Collectors.joining(" -> ")));

        return chain;
    }

    /**
     * 将分类链设置到映射对象中
     */
    private void setCategoryChainToMapping(YzhCategoryMapping mapping, List<CategoryVO> categoryChain) {
        if (categoryChain.isEmpty()) {
            log.warn("分类链为空，无法设置分类信息");
            return;
        }

        log.info("开始设置分类链到映射对象，分类链长度: {}", categoryChain.size());

        // 根据分类链的长度设置对应的分类信息
        if (categoryChain.size() >= 1) {
            CategoryVO firstCategory = categoryChain.get(0);
            mapping.setSystemFirstCategoryId(firstCategory.getId());
            mapping.setSystemFirstCategoryName(firstCategory.getName());
            log.info("✓ 设置一级分类: {} - {}", firstCategory.getId(), firstCategory.getName());
        } else {
            log.warn("✗ 分类链长度不足，无法设置一级分类");
        }

        if (categoryChain.size() >= 2) {
            CategoryVO secondCategory = categoryChain.get(1);
            mapping.setSystemSecondCategoryId(secondCategory.getId());
            mapping.setSystemSecondCategoryName(secondCategory.getName());
            log.info("✓ 设置二级分类: {} - {}", secondCategory.getId(), secondCategory.getName());
        } else {
            log.info("○ 分类链长度为 {}，无二级分类", categoryChain.size());
        }

        if (categoryChain.size() >= 3) {
            CategoryVO thirdCategory = categoryChain.get(2);
            mapping.setSystemLastCategoryId(thirdCategory.getId());
            mapping.setSystemLastCategoryName(thirdCategory.getName());
            log.info("✓ 设置三级分类: {} - {}", thirdCategory.getId(), thirdCategory.getName());
        } else {
            log.info("○ 分类链长度为 {}，无三级分类", categoryChain.size());
        }

        if (categoryChain.size() > 3) {
            log.warn("分类层级超过3级，只保存前3级，总层级数: {}", categoryChain.size());
        }

        // 输出最终设置结果
        log.info("分类链设置完成 - 一级: {}({}), 二级: {}({}), 三级: {}({})",
                mapping.getSystemFirstCategoryName(), mapping.getSystemFirstCategoryId(),
                mapping.getSystemSecondCategoryName(), mapping.getSystemSecondCategoryId(),
                mapping.getSystemLastCategoryName(), mapping.getSystemLastCategoryId());
    }

    /**
     * 验证云中鹤分类数据是否有效
     *
     * @param goodsDetail 商品详情
     * @return true-有效，false-无效
     */
    private boolean isValidYzhCategoryData(YZHGoodsSkuDTO goodsDetail) {
        // 检查一级分类
        boolean hasFirstCategory = StrUtil.isNotBlank(goodsDetail.getFirstCategoryCode()) &&
                                  StrUtil.isNotBlank(goodsDetail.getFirstCategoryName());

        // 检查二级分类
        boolean hasSecondCategory = StrUtil.isNotBlank(goodsDetail.getSecondCategoryCode()) &&
                                   StrUtil.isNotBlank(goodsDetail.getSecondCategoryName());

        // 检查三级分类
        boolean hasThirdCategory = StrUtil.isNotBlank(goodsDetail.getLastCategoryCode()) &&
                                  StrUtil.isNotBlank(goodsDetail.getLastCategoryName());

        // 至少要有一级分类数据才认为是有效的
        if (!hasFirstCategory) {
            log.debug("一级分类数据为空，SKU: {}, 一级分类编码: [{}], 一级分类名称: [{}]",
                     goodsDetail.getGoodsSkuCode(),
                     goodsDetail.getFirstCategoryCode(),
                     goodsDetail.getFirstCategoryName());
            return false;
        }

        // 记录分类数据完整性
        log.debug("云中鹤分类数据验证，SKU: {}, 一级: {}, 二级: {}, 三级: {}",
                 goodsDetail.getGoodsSkuCode(),
                 hasFirstCategory ? "有效" : "无效",
                 hasSecondCategory ? "有效" : "无效",
                 hasThirdCategory ? "有效" : "无效");

        return true;
    }

    /**
     * 转换为DTO
     */
    private YzhCategoryMappingDTO convertToDTO(YzhCategoryMapping mapping) {
        YzhCategoryMappingDTO dto = new YzhCategoryMappingDTO();
        dto.setId(mapping.getId());
        dto.setYzhFirstCategoryCode(mapping.getYzhFirstCategoryCode());
        dto.setYzhFirstCategoryName(mapping.getYzhFirstCategoryName());
        dto.setYzhSecondCategoryCode(mapping.getYzhSecondCategoryCode());
        dto.setYzhSecondCategoryName(mapping.getYzhSecondCategoryName());
        dto.setYzhLastCategoryCode(mapping.getYzhLastCategoryCode());
        dto.setYzhLastCategoryName(mapping.getYzhLastCategoryName());
        dto.setSystemFirstCategoryId(mapping.getSystemFirstCategoryId());
        dto.setSystemFirstCategoryName(mapping.getSystemFirstCategoryName());
        dto.setSystemSecondCategoryId(mapping.getSystemSecondCategoryId());
        dto.setSystemSecondCategoryName(mapping.getSystemSecondCategoryName());
        dto.setSystemLastCategoryId(mapping.getSystemLastCategoryId());
        dto.setSystemLastCategoryName(mapping.getSystemLastCategoryName());
        dto.setMappingType(mapping.getMappingType());
        dto.setSimilarityScore(mapping.getSimilarityScore());
        dto.setMatchLevel(mapping.getMatchLevel());
        dto.setMatchStatus(mapping.getMatchStatus());
        dto.setGoodsSkuCode(mapping.getGoodsSkuCode());
        dto.setGoodsCount(mapping.getGoodsCount());
        dto.setAuditStatus(mapping.getAuditStatus());
        dto.setAuditBy(mapping.getAuditBy());
        dto.setAuditTime(mapping.getAuditTime());
        dto.setAuditRemark(mapping.getAuditRemark());
        dto.setPlatformCode(mapping.getPlatformCode());
        dto.setPlatformName(mapping.getPlatformName());
        // 转换Date类型到LocalDateTime类型
        if (mapping.getCreateTime() != null) {
            dto.setCreateTime(mapping.getCreateTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        if (mapping.getUpdateTime() != null) {
            dto.setUpdateTime(mapping.getUpdateTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        dto.setCreateBy(mapping.getCreateBy());
        dto.setUpdateBy(mapping.getUpdateBy());
        return dto;
    }

    @Override
    public Page<YzhCategoryMapping> getByPage(YzhCategoryMappingSearchParams searchParams) {
        // 构建分页对象
        Page<YzhCategoryMapping> page = PageUtil.initPage(searchParams);

        // 构建查询条件
        LambdaQueryWrapper<YzhCategoryMapping> queryWrapper = new LambdaQueryWrapper<>();

        // 平台名称筛选
        if (StrUtil.isNotBlank(searchParams.getPlatformName())) {
            queryWrapper.like(YzhCategoryMapping::getPlatformName, searchParams.getPlatformName());
        }

        // 平台编码筛选
        if (StrUtil.isNotBlank(searchParams.getPlatformCode())) {
            queryWrapper.eq(YzhCategoryMapping::getPlatformCode, searchParams.getPlatformCode());
        }

        // 云中鹤分类名称筛选
        if (StrUtil.isNotBlank(searchParams.getYzhFirstCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getYzhFirstCategoryName, searchParams.getYzhFirstCategoryName());
        }
        if (StrUtil.isNotBlank(searchParams.getYzhSecondCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getYzhSecondCategoryName, searchParams.getYzhSecondCategoryName());
        }
        if (StrUtil.isNotBlank(searchParams.getYzhLastCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getYzhLastCategoryName, searchParams.getYzhLastCategoryName());
        }

        // 系统分类名称筛选
        if (StrUtil.isNotBlank(searchParams.getSystemFirstCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getSystemFirstCategoryName, searchParams.getSystemFirstCategoryName());
        }
        if (StrUtil.isNotBlank(searchParams.getSystemSecondCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getSystemSecondCategoryName, searchParams.getSystemSecondCategoryName());
        }
        if (StrUtil.isNotBlank(searchParams.getSystemLastCategoryName())) {
            queryWrapper.like(YzhCategoryMapping::getSystemLastCategoryName, searchParams.getSystemLastCategoryName());
        }

        // 映射类型筛选
        if (StrUtil.isNotBlank(searchParams.getMappingType())) {
            queryWrapper.eq(YzhCategoryMapping::getMappingType, searchParams.getMappingType());
        }

        // 匹配状态筛选
        if (StrUtil.isNotBlank(searchParams.getMatchStatus())) {
            queryWrapper.eq(YzhCategoryMapping::getMatchStatus, searchParams.getMatchStatus());
        }

        // 审核状态筛选
        if (StrUtil.isNotBlank(searchParams.getAuditStatus())) {
            queryWrapper.eq(YzhCategoryMapping::getAuditStatus, searchParams.getAuditStatus());
        }

        // 商品SKU编码筛选
        if (StrUtil.isNotBlank(searchParams.getGoodsSkuCode())) {
            queryWrapper.like(YzhCategoryMapping::getGoodsSkuCode, searchParams.getGoodsSkuCode());
        }

        // 匹配层级筛选
        if (searchParams.getMatchLevel() != null) {
            queryWrapper.eq(YzhCategoryMapping::getMatchLevel, searchParams.getMatchLevel());
        }

        // 只查询未删除的记录
        queryWrapper.eq(YzhCategoryMapping::getDeleteFlag, false);

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(YzhCategoryMapping::getCreateTime);

        return this.page(page, queryWrapper);
    }

    @Transactional
    @Override
    @Transactional
    public void updateBatch(List<YzhCategoryMapping> mappings) {
        if (mappings == null || mappings.isEmpty()) {
            log.warn("批量更新分类映射：传入的映射列表为空");
            return;
        }

        try {
            log.info("开始批量更新分类映射，数量: {}", mappings.size());

            // 使用MyBatis-Plus的updateBatchById方法，性能更好且支持事务
            boolean result = this.updateBatchById(mappings);

            if (result) {
                log.info("批量更新分类映射成功，更新数量: {}", mappings.size());
            } else {
                log.error("批量更新分类映射失败");
                throw new RuntimeException("批量更新分类映射失败");
            }
        } catch (Exception e) {
            log.error("批量更新分类映射时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("批量更新分类映射异常: " + e.getMessage(), e);
        }
    }

    @Override
    public List<YzhCategoryMappingSupplierVO> getSuppliers() {

        return mapper.getSuppliers();
    }
}
