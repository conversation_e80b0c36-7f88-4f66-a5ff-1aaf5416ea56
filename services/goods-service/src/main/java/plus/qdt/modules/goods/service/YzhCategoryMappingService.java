package plus.qdt.modules.goods.service;

import jakarta.validation.Valid;
import plus.qdt.modules.goods.entity.dos.YzhCategoryMapping;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO;
import plus.qdt.modules.goods.entity.dto.YzhCategoryMappingSearchParams;
import plus.qdt.modules.goods.entity.vos.CategoryVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import plus.qdt.modules.goods.entity.vos.YzhCategoryMappingSupplierVO;

import java.util.List;

/**
 * 云中鹤分类映射服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
public interface YzhCategoryMappingService extends IService<YzhCategoryMapping> {

    /**
     * 批量处理商品分类映射
     *
     * @param goodsDetailList 商品详情列表
     * @param categoryVOS 系统分类树
     * @return 处理结果
     */
    boolean processCategoryMapping(List<YZHGoodsSkuDTO> goodsDetailList, List<CategoryVO> categoryVOS);

    /**
     * 根据云中鹤分类查找映射记录
     *
     * @param firstCategoryCode 一级分类编码
     * @param secondCategoryCode 二级分类编码
     * @param lastCategoryCode 三级分类编码
     * @return 分类映射记录
     */
    YzhCategoryMapping findMappingByYzhCategory(String firstCategoryCode, String secondCategoryCode, String lastCategoryCode);

    /**
     * 为单个商品创建分类映射
     *
     * @param goodsDetail 商品详情
     * @param categoryVOS 系统分类树
     * @return 映射结果
     */
    YzhCategoryMappingDTO createCategoryMapping(YZHGoodsSkuDTO goodsDetail, List<CategoryVO> categoryVOS);

    /**
     * 自动匹配分类
     *
     * @param yzhCategory 云中鹤分类信息
     * @param categoryVOS 系统分类树
     * @return 匹配结果
     */
    YzhCategoryMappingDTO.CategoryMatchResult autoMatchCategory(
            YzhCategoryMappingDTO.YzhCategoryInfo yzhCategory,
            List<CategoryVO> categoryVOS);

    /**
     * 分页查询云中鹤分类映射
     *
     * @param searchParams 查询参数
     * @return 分页结果
     */
    Page<YzhCategoryMapping> getByPage(YzhCategoryMappingSearchParams searchParams);

    void updateBatch(@Valid List<YzhCategoryMapping> mappings);

    List<YzhCategoryMappingSupplierVO> getSuppliers();

    /**
     * 设置系统分类信息（根据匹配层级设置对应的分类信息）
     *
     * @param mapping 分类映射对象
     * @param match 匹配结果
     * @param categoryVOS 系统分类树
     */
    void setSystemCategoryInfo(YzhCategoryMapping mapping,
                              YzhCategoryMappingDTO.SystemCategoryMatch match,
                              List<CategoryVO> categoryVOS);
}
