package plus.qdt.controller.goods;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.search.entity.dos.EsGoodsIndex;
import plus.qdt.modules.search.entity.dos.EsGoodsRelatedInfo;
import plus.qdt.modules.search.entity.dos.EsSupplierGoodsIndex;
import plus.qdt.modules.search.entity.dto.EsGoodsSearchDTO;
import plus.qdt.modules.search.entity.vo.GoodsAssociateWordsVO;
import plus.qdt.modules.search.service.EsGoodsIndexService;
import plus.qdt.modules.search.service.EsGoodsSearchService;
import plus.qdt.modules.store.client.StoreClient;

import java.util.List;
import java.util.Map;

/**
 * ElasticsearchController
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-03-24 18:32
 */
@RestController
@Tag(name = "ES初始化接口")
@RequestMapping("/goods/elasticsearch")
@RequiredArgsConstructor
public class ElasticsearchGoodsController {

    private final EsGoodsIndexService esGoodsIndexService;

    private final EsGoodsSearchService goodsSearchService;

    private final StoreClient storeClient;


    @Operation(summary = "初始化接口")
    @GetMapping
    public ResultMessage<String> init() {
        esGoodsIndexService.init();
        return ResultUtil.success();
    }

    @Operation(summary = "查看es进度")
    @GetMapping("/progress")
    public ResultMessage<Map<String, Long>> getProgress() {
        return ResultUtil.data(esGoodsIndexService.getProgress());
    }

    @Operation(summary = "从ES中获取商品信息")
    @GetMapping("/es")
    public ResultMessage<Page<EsGoodsIndex>> getGoodsByPageFromEs(EsGoodsSearchDTO goodsSearchParams, PageVO pageVO) {
        pageVO.setNotConvert(true);
        Page<EsGoodsIndex> esGoodsIndices = goodsSearchService.searchGoodsByPage(goodsSearchParams, pageVO, EsGoodsIndex.class);
        String storeId = "";
        Boolean selfOperated = false;
        for (EsGoodsIndex record : esGoodsIndices.getRecords()) {
            if (!record.getStoreId().equals(storeId)) {
                storeId = record.getStoreId();
                selfOperated = storeClient.getStore(record.getStoreId()).getSelfOperated();
            }
            record.setSelfOperated(selfOperated);
        }
        return ResultUtil.data(esGoodsIndices);
    }

    @Operation(summary = "从ES中获取商品信息")
    @GetMapping("/supplier/es")
    public ResultMessage<Page<EsSupplierGoodsIndex>> getSupplierGoodsByPageFromEs(EsGoodsSearchDTO goodsSearchParams, PageVO pageVO) {
        pageVO.setNotConvert(true);
        Page<EsSupplierGoodsIndex> esGoodsIndices = goodsSearchService.searchGoodsByPage(goodsSearchParams, pageVO, EsSupplierGoodsIndex.class);
        return ResultUtil.data(esGoodsIndices);
    }

    @Operation(summary = "从ES中获取相关商品品牌名称，分类名称及属性")
    @GetMapping("/es/related")
    public ResultMessage<EsGoodsRelatedInfo> getGoodsRelatedByPageFromEs(EsGoodsSearchDTO goodsSearchParams, PageVO pageVO) {
        pageVO.setNotConvert(true);
        EsGoodsRelatedInfo selector = goodsSearchService.getSelector(goodsSearchParams, pageVO, EsGoodsIndex.class);
        return ResultUtil.data(selector);
    }

    @Operation(summary = "从ES中获取相关商品品牌名称，分类名称及属性")
    @GetMapping("/supplier/es/related")
    public ResultMessage<EsGoodsRelatedInfo> getSupplierGoodsRelatedByPageFromEs(EsGoodsSearchDTO goodsSearchParams, PageVO pageVO) {
        pageVO.setNotConvert(true);
        EsGoodsRelatedInfo selector = goodsSearchService.getSelector(goodsSearchParams, pageVO, EsSupplierGoodsIndex.class);
        return ResultUtil.data(selector);
    }

    @Operation(summary = "获取联想词")
    @GetMapping("/supplier/associative/words")
    public ResultMessage<List<GoodsAssociateWordsVO>> getSupplierAssociativeWords(String keyword) {
        if (CharSequenceUtil.isBlank(keyword)) {
            return ResultUtil.data(null);
        }
        return ResultUtil.data(goodsSearchService.getAssociativeWords(keyword, EsSupplierGoodsIndex.class));
    }

    @Operation(summary = "获取联想词")
    @GetMapping("/associative/words")
    public ResultMessage<List<GoodsAssociateWordsVO>> getAssociativeWords(String keyword) {
        if (CharSequenceUtil.isBlank(keyword)) {
            return ResultUtil.data(null);
        }
        return ResultUtil.data(goodsSearchService.getAssociativeWords(keyword, EsGoodsIndex.class));
    }

}
