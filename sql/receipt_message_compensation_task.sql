-- 发票消息补偿定时任务配置SQL
-- 用于新增发票消息补偿定时任务到 scheduled_task_config 表

INSERT INTO `scheduled_task_config` (
    `task_name`,
    `task_code`,
    `task_description`,
    `task_type`,
    `cron_expression`,
    `fixed_rate`,
    `fixed_delay`,
    `task_status`,
    `task_params`,
    `last_execute_time`,
    `next_execute_time`,
    `execute_count`,
    `success_count`,
    `fail_count`,
    `last_execute_result`,
    `last_execute_message`,
    `creator`,
    `create_time`,
    `updater`,
    `update_time`,
    `deleted`
) VALUES (
    '发票消息补偿任务',                                    -- task_name: 任务名称
    'RECEIPT_MESSAGE_COMPENSATION',                        -- task_code: 任务编码（唯一标识）
    '每1小时执行一次，检查MessageRecord里的发票业务是否有没有消费的记录，如果有则重发该条消息到ReceiptMessageListener', -- task_description: 任务描述
    'COMPENSATION',                                        -- task_type: 任务类型
    '0 0 */1 * * ?',                                      -- cron_expression: Cron表达式（每小时执行一次）
    3600000,                                              -- fixed_rate: 固定频率（毫秒，1小时）
    NULL,                                                 -- fixed_delay: 固定延迟（不使用）
    'ENABLED',                                            -- task_status: 任务状态（启用）
    '{"bizType":"RECEIPT_CREATE","maxRetryHours":24,"batchSize":100}', -- task_params: 任务参数（JSON格式）
    NULL,                                                 -- last_execute_time: 上次执行时间
    NULL,                                                 -- next_execute_time: 下次执行时间
    0,                                                    -- execute_count: 执行次数
    0,                                                    -- success_count: 成功次数
    0,                                                    -- fail_count: 失败次数
    NULL,                                                 -- last_execute_result: 上次执行结果
    NULL,                                                 -- last_execute_message: 上次执行消息
    'system',                                             -- creator: 创建人
    NOW(),                                                -- create_time: 创建时间
    'system',                                             -- updater: 更新人
    NOW(),                                                -- update_time: 更新时间
    0                                                     -- deleted: 是否删除（0-未删除）
);

-- 验证插入结果
SELECT * FROM `scheduled_task_config` WHERE `task_code` = 'RECEIPT_MESSAGE_COMPENSATION';

-- 可选：如果需要立即禁用任务，可以执行以下SQL
-- UPDATE `scheduled_task_config` SET `task_status` = 'DISABLED' WHERE `task_code` = 'RECEIPT_MESSAGE_COMPENSATION';

-- 可选：如果需要修改执行频率（例如改为30分钟执行一次），可以执行以下SQL
-- UPDATE `scheduled_task_config` SET 
--     `cron_expression` = '0 */30 * * * ?',
--     `fixed_rate` = 1800000,
--     `updater` = 'admin',
--     `update_time` = NOW()
-- WHERE `task_code` = 'RECEIPT_MESSAGE_COMPENSATION';

-- 任务参数说明：
-- {
--   "bizType": "RECEIPT_CREATE",           // 业务类型，固定为发票创建
--   "maxRetryHours": 24,                   // 最大重试时间范围（小时），超过此时间的消息不再重试
--   "batchSize": 100                       // 每次处理的消息批次大小
-- }

-- 任务状态说明：
-- ENABLED  - 启用状态，任务会正常执行
-- DISABLED - 禁用状态，任务不会执行

-- Cron表达式说明：
-- '0 0 */1 * * ?' - 每小时的0分0秒执行一次
-- '0 */30 * * * ?' - 每30分钟执行一次
-- '0 0 */2 * * ?' - 每2小时执行一次

-- 使用示例：
-- 1. 执行上述INSERT语句创建任务配置
-- 2. 系统会自动根据配置执行发票消息补偿任务
-- 3. 可以通过修改task_status来启用/禁用任务
-- 4. 可以通过修改fixed_rate或cron_expression来调整执行频率
-- 5. 可以通过task_params来调整任务参数
